#!/usr/bin/env python3
"""
Quick start script for <PERSON>.

This script provides an easy way to get <PERSON> up and running
with minimal configuration.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import torch
        import fastapi
        import uvicorn
        print("✓ Core dependencies found")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        return False


def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("✗ Failed to install dependencies")
        return False


def create_directories():
    """Create necessary directories."""
    directories = ["models", "logs", "checkpoints", "data"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Quick start Holly AI system")
    parser.add_argument("--install-deps", action="store_true", help="Install dependencies")
    parser.add_argument("--port", type=int, default=8000, help="Port to run on")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    print("🌟 Holly - Collaborative AI System")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        if args.install_deps:
            if not install_dependencies():
                sys.exit(1)
        else:
            print("Run with --install-deps to install missing dependencies")
            sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Start Holly
    print(f"\n🚀 Starting Holly on http://{args.host}:{args.port}")
    print("Press Ctrl+C to stop")
    print("-" * 40)
    
    try:
        # Import and run Holly
        from holly.server import main as holly_main
        
        # Set up arguments for Holly
        sys.argv = [
            "holly",
            "--host", args.host,
            "--port", str(args.port)
        ]
        
        if args.debug:
            sys.argv.append("--debug")
        
        holly_main()
        
    except KeyboardInterrupt:
        print("\n👋 Holly stopped. Goodbye!")
    except Exception as e:
        print(f"\n❌ Error starting Holly: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
