"""
Main entry point for Holly server.

This module provides the command-line interface for starting
<PERSON>'s web server and managing the AI system.
"""

import argparse
import logging
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from holly.interface.server import HollyServer
from holly.core.model import HollyModel
from holly.core.tokenizer import HollyTokenizer


def setup_logging(debug: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('holly.log')
        ]
    )


def create_initial_model(save_path: str = "models/holly_initial.pt"):
    """Create and save an initial Holly model."""
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    print("Creating initial Holly model...")
    
    # Create tokenizer and model
    tokenizer = HollyTokenizer()
    model = HollyModel(
        vocab_size=tokenizer.get_vocab_size(),
        d_model=256,  # Start small for development
        n_layers=4,
        n_heads=8,
        max_seq_length=1024
    )
    
    # Save initial model
    model.save_model(save_path)
    
    # Save tokenizer
    tokenizer_path = save_path.replace('.pt', '_tokenizer.json')
    tokenizer.save_vocabulary(tokenizer_path)
    
    print(f"Initial model saved to: {save_path}")
    print(f"Tokenizer saved to: {tokenizer_path}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    return save_path


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Holly - Collaborative AI System")
    
    parser.add_argument(
        "--host", 
        default="localhost", 
        help="Host to bind the server to"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="Port to bind the server to"
    )
    parser.add_argument(
        "--model-path", 
        help="Path to saved Holly model (creates new if not provided)"
    )
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug mode"
    )
    parser.add_argument(
        "--create-model", 
        action="store_true", 
        help="Create a new initial model and exit"
    )
    parser.add_argument(
        "--model-output", 
        default="models/holly_initial.pt", 
        help="Output path for new model"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    logger = logging.getLogger("holly.main")
    
    # Handle model creation
    if args.create_model:
        create_initial_model(args.model_output)
        return
    
    # Determine model path
    model_path = args.model_path
    if not model_path:
        default_path = "models/holly_initial.pt"
        if not os.path.exists(default_path):
            logger.info("No model found, creating initial model...")
            model_path = create_initial_model(default_path)
        else:
            model_path = default_path
    
    # Verify model exists
    if not os.path.exists(model_path):
        logger.error(f"Model file not found: {model_path}")
        sys.exit(1)
    
    # Create and start server
    try:
        logger.info("Initializing Holly server...")
        server = HollyServer(
            model_path=model_path,
            host=args.host,
            port=args.port,
            debug=args.debug
        )
        
        logger.info(f"Holly is ready! Open http://{args.host}:{args.port} in your browser")
        server.run()
        
    except KeyboardInterrupt:
        logger.info("Shutting down Holly server...")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
