# Holly Configuration File
# This file contains configuration settings for <PERSON>'s behavior and capabilities

# Model Configuration
model:
  # Architecture parameters
  d_model: 256              # Model dimension (start small for development)
  n_layers: 4               # Number of transformer layers
  n_heads: 8                # Number of attention heads
  d_ff: 1024               # Feed-forward dimension
  max_seq_length: 1024      # Maximum sequence length
  dropout: 0.1              # Dropout rate
  activation: "gelu"        # Activation function
  
  # Advanced features
  use_gating: false         # Enable gating in feed-forward layers
  adaptive_norm: true       # Enable adaptive layer normalization
  reasoning_mode: true      # Enable reasoning capabilities

# Tokenizer Configuration
tokenizer:
  vocab_size: 8192          # Initial vocabulary size
  min_frequency: 2          # Minimum frequency for new tokens
  special_tokens:
    pad: "<pad>"
    unk: "<unk>"
    bos: "<bos>"
    eos: "<eos>"
    human: "<human>"
    holly: "<holly>"
    think: "<think>"
    think_end: "</think>"
    uncertain: "<uncertain>"
    question: "<question>"

# Training Configuration
training:
  learning_rate: 1.0e-4     # Base learning rate
  max_grad_norm: 1.0        # Gradient clipping threshold
  warmup_steps: 100         # Learning rate warmup steps
  weight_decay: 0.01        # Weight decay for regularization
  
  # Safety settings
  safety_checks: true       # Enable safety monitoring
  max_loss_spike: 3.0       # Maximum allowed loss spike multiplier
  gradient_explosion_threshold: 5.0  # Gradient explosion detection
  
  # Learning session settings
  auto_save_interval: 100   # Auto-save every N learning steps
  checkpoint_on_feedback: true  # Create checkpoint after feedback

# Memory Configuration
memory:
  episodic_memory: true     # Enable episodic memory system
  max_context_length: 2048  # Maximum conversation context length
  memory_db_path: "data/holly_memory.db"
  
  # Memory importance scoring
  conversation_importance: 0.7
  feedback_importance: 0.9
  learning_importance: 0.8
  error_importance: 0.6

# Server Configuration
server:
  host: "localhost"         # Server host
  port: 8000               # Server port
  debug: false             # Debug mode
  
  # WebSocket settings
  max_connections: 100      # Maximum concurrent connections
  ping_interval: 30         # WebSocket ping interval (seconds)
  
  # API settings
  max_request_size: 1048576  # Maximum request size (1MB)
  request_timeout: 30       # Request timeout (seconds)

# Generation Configuration
generation:
  default_temperature: 0.8  # Default sampling temperature
  default_top_k: 50         # Default top-k sampling
  default_top_p: 0.9        # Default top-p (nucleus) sampling
  max_generation_length: 200  # Maximum generation length
  
  # Reasoning settings
  show_reasoning_by_default: false
  reasoning_detail_level: "medium"  # low, medium, high
  
  # Safety settings
  content_filter: true      # Enable content filtering
  repetition_penalty: 1.1   # Repetition penalty

# Logging Configuration
logging:
  level: "INFO"             # Logging level (DEBUG, INFO, WARNING, ERROR)
  log_file: "logs/holly.log"
  max_log_size: 10485760    # Maximum log file size (10MB)
  backup_count: 5           # Number of backup log files
  
  # Component-specific logging
  components:
    model: "INFO"
    trainer: "INFO"
    server: "INFO"
    memory: "WARNING"

# Experimental Features
experimental:
  self_modification: true   # Enable self-modification capabilities
  architecture_proposals: true  # Allow architecture change proposals
  dynamic_vocabulary: true  # Enable dynamic vocabulary expansion
  
  # Research features
  attention_visualization: true
  gradient_analysis: true
  performance_profiling: false

# File Paths
paths:
  models_dir: "models"
  checkpoints_dir: "checkpoints"
  logs_dir: "logs"
  data_dir: "data"
  
  # Default file names
  default_model: "holly_initial.pt"
  default_tokenizer: "holly_tokenizer.json"
  config_backup: "config_backup.yaml"

# Development Settings
development:
  auto_reload: false        # Auto-reload on code changes
  profiling: false          # Enable performance profiling
  test_mode: false          # Enable test mode features
  
  # Debug features
  save_attention_maps: false
  log_gradient_norms: false
  track_parameter_changes: false
