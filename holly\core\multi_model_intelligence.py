"""
Multi-Model Intelligence System for <PERSON>.

This system gives <PERSON> access to ALL the top models and lets her
intelligently choose which one to use for each specific task.
"""

import torch
import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from transformers import AutoTokenizer, AutoModelForCausalLM
import numpy as np
from datetime import datetime
import json

class ModelCapability:
    """Represents a model's capabilities and characteristics."""
    
    def __init__(self, name: str, model_id: str, creator: str, 
                 intelligence_score: int, context_window: int, 
                 speed_rating: str, cost_rating: str, specialties: List[str]):
        self.name = name
        self.model_id = model_id
        self.creator = creator
        self.intelligence_score = intelligence_score
        self.context_window = context_window
        self.speed_rating = speed_rating  # 'fast', 'medium', 'slow'
        self.cost_rating = cost_rating    # 'free', 'cheap', 'expensive'
        self.specialties = specialties    # ['reasoning', 'conversation', 'coding', etc.]
        
        # Performance tracking
        self.usage_count = 0
        self.avg_response_time = 0.0
        self.success_rate = 1.0
        self.user_satisfaction = 0.8
        
    def get_suitability_score(self, task_type: str, context_length: int, 
                            speed_priority: float, cost_priority: float) -> float:
        """Calculate how suitable this model is for a specific task."""
        score = 0.0
        
        # Base intelligence score (0-1)
        score += (self.intelligence_score / 70) * 0.4
        
        # Specialty match bonus
        if task_type in self.specialties:
            score += 0.3
        elif any(spec in task_type for spec in self.specialties):
            score += 0.15
        
        # Context window suitability
        if context_length <= self.context_window:
            score += 0.1
        else:
            score -= 0.2  # Penalty for insufficient context
        
        # Speed consideration
        speed_scores = {'fast': 0.1, 'medium': 0.05, 'slow': 0.0}
        score += speed_scores.get(self.speed_rating, 0) * speed_priority
        
        # Cost consideration
        cost_scores = {'free': 0.1, 'cheap': 0.05, 'expensive': 0.0}
        score += cost_scores.get(self.cost_rating, 0) * cost_priority
        
        # Performance history
        score += (self.success_rate - 0.5) * 0.1
        score += (self.user_satisfaction - 0.5) * 0.1
        
        return max(0.0, min(1.0, score))

class MultiModelIntelligence:
    """Holly's multi-model intelligence system."""
    
    def __init__(self):
        self.models = {}
        self.loaded_models = {}
        self.model_capabilities = {}
        self.decision_history = []
        self.performance_stats = {}
        
        # Holly's decision-making preferences
        self.preferences = {
            'intelligence_weight': 0.4,
            'speed_weight': 0.3,
            'cost_weight': 0.2,
            'specialty_weight': 0.1
        }
        
        self.logger = logging.getLogger('holly.multi_model')
        self._initialize_model_catalog()
    
    def _initialize_model_catalog(self):
        """Initialize the catalog of available models."""
        print("🧠 Initializing Holly's Multi-Model Intelligence System...")
        
        # Define all available models with their capabilities
        model_configs = [
            # Top Intelligence Models
            ModelCapability(
                "qwen3-235b", "Qwen/Qwen3-235B-Instruct", "Alibaba",
                intelligence_score=62, context_window=128000,
                speed_rating="slow", cost_rating="expensive",
                specialties=["reasoning", "analysis", "complex_problems", "research"]
            ),
            ModelCapability(
                "deepseek-v3", "deepseek-ai/DeepSeek-V3", "DeepSeek", 
                intelligence_score=53, context_window=128000,
                speed_rating="medium", cost_rating="cheap",
                specialties=["reasoning", "coding", "analysis", "problem_solving"]
            ),
            ModelCapability(
                "qwen2.5-72b", "Qwen/Qwen2.5-72B-Instruct", "Alibaba",
                intelligence_score=40, context_window=131000,
                speed_rating="medium", cost_rating="free",
                specialties=["conversation", "general", "reasoning", "multilingual"]
            ),
            ModelCapability(
                "llama-3.3-70b", "meta-llama/Llama-3.3-70B-Instruct", "Meta",
                intelligence_score=41, context_window=128000,
                speed_rating="medium", cost_rating="cheap",
                specialties=["conversation", "general", "creative", "helpful"]
            ),
            
            # Fast Response Models
            ModelCapability(
                "qwen3-32b", "Qwen/Qwen3-32B-Instruct", "Alibaba",
                intelligence_score=44, context_window=128000,
                speed_rating="fast", cost_rating="cheap",
                specialties=["conversation", "quick_responses", "general"]
            ),
            ModelCapability(
                "gemma-3-27b", "google/gemma-3-27b-it", "Google",
                intelligence_score=38, context_window=128000,
                speed_rating="fast", cost_rating="free",
                specialties=["conversation", "helpful", "fast_responses"]
            ),
            ModelCapability(
                "qwen3-14b", "Qwen/Qwen3-14B-Instruct", "Alibaba",
                intelligence_score=41, context_window=128000,
                speed_rating="fast", cost_rating="cheap",
                specialties=["conversation", "general", "quick_responses"]
            ),
            
            # Specialized Models
            ModelCapability(
                "qwen2.5-coder-32b", "Qwen/Qwen2.5-Coder-32B-Instruct", "Alibaba",
                intelligence_score=36, context_window=131000,
                speed_rating="medium", cost_rating="cheap",
                specialties=["coding", "programming", "technical", "debugging"]
            ),
            ModelCapability(
                "deepseek-coder-v2", "deepseek-ai/DeepSeek-Coder-V2-Instruct", "DeepSeek",
                intelligence_score=29, context_window=128000,
                speed_rating="fast", cost_rating="free",
                specialties=["coding", "programming", "technical"]
            ),
            
            # Lightweight Fast Models
            ModelCapability(
                "qwen3-8b", "Qwen/Qwen3-8B-Instruct", "Alibaba",
                intelligence_score=37, context_window=128000,
                speed_rating="fast", cost_rating="free",
                specialties=["conversation", "quick_responses", "general"]
            ),
            ModelCapability(
                "gemma-3-12b", "google/gemma-3-12b-it", "Google",
                intelligence_score=34, context_window=128000,
                speed_rating="fast", cost_rating="free",
                specialties=["conversation", "helpful", "efficient"]
            ),
            ModelCapability(
                "llama-3.1-8b", "meta-llama/Llama-3.1-8B-Instruct", "Meta",
                intelligence_score=24, context_window=128000,
                speed_rating="fast", cost_rating="free",
                specialties=["conversation", "general", "fast_responses"]
            ),
            
            # Ultra-fast for simple tasks
            ModelCapability(
                "qwen3-4b", "Qwen/Qwen3-4B-Instruct", "Alibaba",
                intelligence_score=35, context_window=32000,
                speed_rating="fast", cost_rating="free",
                specialties=["simple_conversation", "quick_responses", "greetings"]
            ),
            ModelCapability(
                "gemma-3-4b", "google/gemma-3-4b-it", "Google",
                intelligence_score=24, context_window=128000,
                speed_rating="fast", cost_rating="free",
                specialties=["simple_conversation", "quick_responses", "basic_tasks"]
            )
        ]
        
        # Store model capabilities
        for model_cap in model_configs:
            self.model_capabilities[model_cap.name] = model_cap
        
        print(f"✅ Initialized {len(model_configs)} models in Holly's arsenal")
        
        # Load a few key models immediately
        self._preload_essential_models()
    
    def _preload_essential_models(self):
        """Preload essential models for immediate use."""
        essential_models = [
            "qwen3-8b",      # Fast general purpose
            "gemma-3-4b",    # Ultra-fast for simple tasks
            "qwen2.5-72b"    # High intelligence when needed
        ]
        
        print("🚀 Preloading essential models...")
        
        for model_name in essential_models:
            try:
                self._load_model_if_needed(model_name)
                print(f"   ✅ {model_name} ready")
            except Exception as e:
                print(f"   ⚠️ {model_name} failed to load: {e}")
                # Continue with other models
    
    def _load_model_if_needed(self, model_name: str) -> bool:
        """Load a model if it's not already loaded."""
        if model_name in self.loaded_models:
            return True
        
        if model_name not in self.model_capabilities:
            self.logger.error(f"Unknown model: {model_name}")
            return False
        
        try:
            model_cap = self.model_capabilities[model_name]
            
            print(f"📥 Loading {model_name} ({model_cap.creator})...")
            
            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(model_cap.model_id)
            model = AutoModelForCausalLM.from_pretrained(
                model_cap.model_id,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            # Set padding token
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            # Store loaded model
            self.loaded_models[model_name] = {
                'model': model,
                'tokenizer': tokenizer,
                'load_time': datetime.now(),
                'usage_count': 0
            }
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load {model_name}: {e}")
            return False
    
    async def choose_best_model(self, task_description: str, context: str = "", 
                              user_preferences: Dict = None) -> str:
        """Holly intelligently chooses the best model for the task."""
        
        # Analyze the task
        task_analysis = self._analyze_task(task_description, context)
        
        # Get user preferences or use defaults
        prefs = user_preferences or {}
        speed_priority = prefs.get('speed_priority', 0.3)
        cost_priority = prefs.get('cost_priority', 0.2)
        intelligence_priority = prefs.get('intelligence_priority', 0.5)
        
        # Calculate suitability scores for all models
        model_scores = {}
        
        for model_name, model_cap in self.model_capabilities.items():
            score = model_cap.get_suitability_score(
                task_analysis['type'],
                task_analysis['estimated_context_length'],
                speed_priority,
                cost_priority
            )
            
            # Boost score for loaded models (faster to use)
            if model_name in self.loaded_models:
                score += 0.05
            
            model_scores[model_name] = score
        
        # Sort by score
        ranked_models = sorted(model_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Holly's decision-making process
        best_model = ranked_models[0][0]
        best_score = ranked_models[0][1]
        
        # Record decision
        decision = {
            'timestamp': datetime.now().isoformat(),
            'task_description': task_description,
            'task_analysis': task_analysis,
            'chosen_model': best_model,
            'score': best_score,
            'alternatives': ranked_models[1:4],  # Top 3 alternatives
            'reasoning': self._generate_decision_reasoning(
                best_model, task_analysis, ranked_models
            )
        }
        
        self.decision_history.append(decision)
        
        # Load the chosen model if needed
        if not self._load_model_if_needed(best_model):
            # Fallback to next best loaded model
            for model_name, score in ranked_models[1:]:
                if model_name in self.loaded_models:
                    best_model = model_name
                    break
            else:
                # Ultimate fallback to any loaded model
                if self.loaded_models:
                    best_model = list(self.loaded_models.keys())[0]
                else:
                    raise Exception("No models available!")
        
        return best_model
    
    def _analyze_task(self, task_description: str, context: str = "") -> Dict:
        """Analyze the task to determine its characteristics."""
        task_lower = task_description.lower()
        context_lower = context.lower()
        combined = f"{task_lower} {context_lower}"
        
        # Determine task type
        task_type = "general"
        
        if any(word in combined for word in ['code', 'program', 'debug', 'function', 'algorithm']):
            task_type = "coding"
        elif any(word in combined for word in ['analyze', 'research', 'complex', 'solve', 'problem']):
            task_type = "analysis"
        elif any(word in combined for word in ['reason', 'think', 'logic', 'explain why']):
            task_type = "reasoning"
        elif any(word in combined for word in ['create', 'write', 'story', 'poem', 'creative']):
            task_type = "creative"
        elif any(word in combined for word in ['hello', 'hi', 'how are you', 'what is your name']):
            task_type = "simple_conversation"
        elif any(word in combined for word in ['chat', 'talk', 'discuss', 'conversation']):
            task_type = "conversation"
        elif any(word in combined for word in ['quick', 'fast', 'briefly', 'short']):
            task_type = "quick_responses"
        
        # Estimate complexity
        complexity = "medium"
        if len(task_description) < 20:
            complexity = "simple"
        elif len(task_description) > 100 or "complex" in combined:
            complexity = "complex"
        
        # Estimate context length needed
        estimated_context = len(task_description) + len(context) + 500  # Buffer for response
        
        return {
            'type': task_type,
            'complexity': complexity,
            'estimated_context_length': estimated_context,
            'urgency': 'high' if any(word in combined for word in ['urgent', 'quick', 'fast']) else 'normal'
        }
    
    def _generate_decision_reasoning(self, chosen_model: str, task_analysis: Dict, 
                                   ranked_models: List[Tuple[str, float]]) -> str:
        """Generate Holly's reasoning for her model choice."""
        model_cap = self.model_capabilities[chosen_model]
        
        reasoning = f"I chose {chosen_model} ({model_cap.creator}) because:\n"
        reasoning += f"• Task type: {task_analysis['type']} (matches my specialties: {', '.join(model_cap.specialties)})\n"
        reasoning += f"• Intelligence score: {model_cap.intelligence_score}/70\n"
        reasoning += f"• Speed rating: {model_cap.speed_rating}\n"
        reasoning += f"• Cost efficiency: {model_cap.cost_rating}\n"
        
        if len(ranked_models) > 1:
            alt_model, alt_score = ranked_models[1]
            reasoning += f"\nAlternative considered: {alt_model} (score: {alt_score:.3f})"
        
        return reasoning
    
    async def generate_with_chosen_model(self, model_name: str, prompt: str, 
                                       **generation_kwargs) -> Dict[str, Any]:
        """Generate response using the chosen model."""
        if model_name not in self.loaded_models:
            raise ValueError(f"Model {model_name} not loaded")
        
        start_time = time.time()
        
        try:
            model_data = self.loaded_models[model_name]
            model = model_data['model']
            tokenizer = model_data['tokenizer']
            
            # Tokenize input
            inputs = tokenizer.encode(prompt, return_tensors='pt', truncation=True, max_length=4000)
            
            # Move to device
            if torch.cuda.is_available():
                inputs = inputs.cuda()
            
            # Generate
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=inputs.size(1) + generation_kwargs.get('max_length', 150),
                    temperature=generation_kwargs.get('temperature', 0.8),
                    top_k=generation_kwargs.get('top_k', 50),
                    top_p=generation_kwargs.get('top_p', 0.9),
                    do_sample=generation_kwargs.get('do_sample', True),
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    no_repeat_ngram_size=3
                )
            
            # Decode response
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract new content
            if prompt in generated_text:
                response = generated_text[len(prompt):].strip()
            else:
                response = generated_text.strip()
            
            # Update usage stats
            model_data['usage_count'] += 1
            response_time = time.time() - start_time
            
            # Update model performance
            model_cap = self.model_capabilities[model_name]
            model_cap.usage_count += 1
            model_cap.avg_response_time = (
                (model_cap.avg_response_time * (model_cap.usage_count - 1) + response_time) 
                / model_cap.usage_count
            )
            
            return {
                'response': response,
                'model_used': model_name,
                'response_time': response_time,
                'model_info': {
                    'creator': model_cap.creator,
                    'intelligence_score': model_cap.intelligence_score,
                    'specialties': model_cap.specialties
                }
            }
            
        except Exception as e:
            self.logger.error(f"Generation failed with {model_name}: {e}")
            
            # Update failure rate
            model_cap = self.model_capabilities[model_name]
            model_cap.success_rate = max(0.1, model_cap.success_rate - 0.1)
            
            raise e
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get status of all models."""
        return {
            'total_models': len(self.model_capabilities),
            'loaded_models': len(self.loaded_models),
            'model_details': {
                name: {
                    'loaded': name in self.loaded_models,
                    'intelligence_score': cap.intelligence_score,
                    'usage_count': cap.usage_count,
                    'avg_response_time': cap.avg_response_time,
                    'success_rate': cap.success_rate,
                    'specialties': cap.specialties
                }
                for name, cap in self.model_capabilities.items()
            },
            'recent_decisions': self.decision_history[-5:] if self.decision_history else []
        }
    
    def get_holly_reasoning_about_models(self) -> str:
        """Get Holly's thoughts about her model collection."""
        total_models = len(self.model_capabilities)
        loaded_models = len(self.loaded_models)
        
        reasoning = f"I have access to {total_models} different AI models, with {loaded_models} currently loaded and ready. "
        reasoning += "This gives me incredible flexibility to choose the perfect tool for each task!\n\n"
        
        reasoning += "My model selection strategy:\n"
        reasoning += "• For complex reasoning: Qwen3-235B or DeepSeek-V3\n"
        reasoning += "• For fast conversations: Qwen3-8B or Gemma-3-12B\n"
        reasoning += "• For coding tasks: Qwen2.5-Coder or DeepSeek-Coder\n"
        reasoning += "• For simple greetings: Qwen3-4B or Gemma-3-4B\n\n"
        
        if self.decision_history:
            recent_models = [d['chosen_model'] for d in self.decision_history[-10:]]
            most_used = max(set(recent_models), key=recent_models.count)
            reasoning += f"Recently, I've been using {most_used} most often, "
            reasoning += "which shows I'm adapting to the types of conversations we're having!"
        
        return reasoning
