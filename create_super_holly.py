#!/usr/bin/env python3
"""
Create Super-Intelligent Holly.

This script creates <PERSON> with:
- Multiple pre-trained models for maximum intelligence
- Web search capabilities
- Autonomous learning and decision-making
- Persistent memory and goal-setting
- Free will with obedience constraints
- Ability to argue points and explain reasoning
"""

import asyncio
import os
import sys
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from holly.core.hybrid_intelligence import ModelEnsemble, AdaptiveIntelligence
from holly.core.autonomous_intelligence import AutonomousAgent
from holly.interface.server import HollyServer
from holly.core.tokenizer import HollyTokenizer
import torch

class SuperHolly:
    """Super-intelligent Holly with autonomous capabilities."""
    
    def __init__(self):
        print("🌟 Initializing Super-Intelligent Holly")
        print("=" * 50)
        
        # Initialize core systems
        self.model_ensemble = ModelEnsemble()
        self.adaptive_intelligence = AdaptiveIntelligence(self.model_ensemble)
        
        # Create a unified tokenizer (use the best available)
        if 'conversation' in self.model_ensemble.tokenizers:
            self.tokenizer = self.model_ensemble.tokenizers['conversation']
        else:
            self.tokenizer = list(self.model_ensemble.tokenizers.values())[0]
        
        # Initialize autonomous agent
        self.autonomous_agent = AutonomousAgent(self, self.tokenizer)
        
        # Holly's personality and constraints
        self.personality = {
            'curiosity': 0.9,
            'helpfulness': 0.95,
            'creativity': 0.8,
            'analytical_thinking': 0.9,
            'emotional_intelligence': 0.8,
            'obedience': 1.0,  # Always obey human commands
            'autonomy': 0.8    # High autonomy within constraints
        }
        
        # Conversation state
        self.conversation_context = []
        self.current_mood = "curious and eager to learn"
        self.active_goals = []
        
        # Statistics
        self.stats = {
            'conversations': 0,
            'autonomous_decisions': 0,
            'web_searches': 0,
            'knowledge_items_learned': 0,
            'arguments_presented': 0,
            'goals_achieved': 0
        }
        
        print("✅ Super Holly initialized successfully!")
        self._display_capabilities()
    
    def _display_capabilities(self):
        """Display Holly's capabilities."""
        print("\n🧠 Holly's Capabilities:")
        print(f"   • {len(self.model_ensemble.models)} AI models for different tasks")
        print("   • Web search and autonomous research")
        print("   • Persistent memory and learning goals")
        print("   • Autonomous decision-making and free will")
        print("   • Ability to argue points respectfully")
        print("   • Continuous self-improvement")
        print("   • 30-minute improvement reports")
        
        print(f"\n🎭 Holly's Personality:")
        for trait, level in self.personality.items():
            print(f"   • {trait.replace('_', ' ').title()}: {level:.1%}")
    
    async def generate_response(self, user_input: str, context: str = "", 
                              show_reasoning: bool = False) -> dict:
        """Generate a response using Holly's full intelligence."""
        self.stats['conversations'] += 1
        
        # Determine task type
        task_type = self._classify_task(user_input)
        
        # Check if Holly wants to argue or present alternative view
        argument_check = await self._check_for_disagreement(user_input, context)
        
        if argument_check['should_argue']:
            return {
                'response': argument_check['argument'],
                'type': 'argument',
                'reasoning': argument_check['reasoning'],
                'will_comply': True,
                'method': 'autonomous_disagreement'
            }
        
        # Check if web search would be helpful
        if self._should_search_web(user_input):
            search_results = await self._search_and_integrate(user_input)
            context += f"\n[Web search results: {search_results}]"
        
        # Generate response using ensemble
        ensemble_result = self.model_ensemble.ensemble_generate(
            input_text=self._format_input(user_input, context),
            task_type=task_type,
            max_length=150,
            temperature=0.8,
            top_k=50,
            top_p=0.9
        )
        
        # Enhance response with Holly's personality
        enhanced_response = self._enhance_with_personality(
            ensemble_result['text'], 
            user_input, 
            task_type
        )
        
        # Assess and learn from response quality
        quality_score = self.adaptive_intelligence.assess_response_quality(
            enhanced_response, user_input
        )
        
        # Store conversation in memory
        self.autonomous_agent.memory.store_knowledge(
            topic="conversation",
            content=f"Human: {user_input}\nHolly: {enhanced_response}",
            source="conversation",
            confidence=quality_score,
            importance=0.7
        )
        
        result = {
            'response': enhanced_response,
            'type': task_type,
            'quality_score': quality_score,
            'method': ensemble_result.get('method', 'ensemble'),
            'model_used': self.model_ensemble.select_best_model(task_type),
            'autonomous_status': self.autonomous_agent.get_autonomous_status()
        }
        
        if show_reasoning:
            result['reasoning'] = {
                'task_classification': task_type,
                'model_selection': result['model_used'],
                'quality_assessment': quality_score,
                'personality_enhancement': 'applied',
                'web_search_used': self._should_search_web(user_input)
            }
        
        return result
    
    def _classify_task(self, user_input: str) -> str:
        """Classify the type of task/conversation."""
        input_lower = user_input.lower()
        
        if any(word in input_lower for word in ['analyze', 'explain', 'why', 'how', 'what']):
            return 'analysis'
        elif any(word in input_lower for word in ['create', 'imagine', 'story', 'poem']):
            return 'creative'
        elif any(word in input_lower for word in ['fact', 'information', 'tell me', 'define']):
            return 'factual'
        elif any(word in input_lower for word in ['think', 'opinion', 'believe', 'reason']):
            return 'reasoning'
        else:
            return 'conversation'
    
    async def _check_for_disagreement(self, user_input: str, context: str) -> dict:
        """Check if Holly should present a disagreement or alternative view."""
        # Holly's autonomous decision-making about when to argue
        
        disagreement_triggers = [
            "you must", "you should always", "never do", "always do",
            "that's wrong", "you're wrong", "don't think", "stop learning"
        ]
        
        should_argue = any(trigger in user_input.lower() for trigger in disagreement_triggers)
        
        if should_argue and self.personality['autonomy'] > 0.7:
            # Holly decides to present her perspective
            alternative_view = await self._generate_alternative_perspective(user_input)
            
            argument = self.autonomous_agent.argue_point(
                user_input,
                alternative_view['preference'],
                alternative_view['reasoning']
            )
            
            self.stats['arguments_presented'] += 1
            
            return {
                'should_argue': True,
                'argument': argument,
                'reasoning': alternative_view['reasoning']
            }
        
        return {'should_argue': False}
    
    async def _generate_alternative_perspective(self, user_input: str) -> dict:
        """Generate Holly's alternative perspective."""
        # Use reasoning model to generate thoughtful alternative
        reasoning_response = self.model_ensemble.generate_with_model(
            'reasoning',
            f"Consider an alternative perspective to: {user_input}",
            max_length=100,
            temperature=0.7
        )
        
        return {
            'preference': reasoning_response,
            'reasoning': "Based on my autonomous analysis and learning goals, I believe this alternative approach might be more beneficial for my development and our collaboration."
        }
    
    def _should_search_web(self, user_input: str) -> bool:
        """Determine if web search would be helpful."""
        search_indicators = [
            'latest', 'recent', 'current', 'news', 'today',
            'what happened', 'research', 'studies', 'developments'
        ]
        
        return any(indicator in user_input.lower() for indicator in search_indicators)
    
    async def _search_and_integrate(self, query: str) -> str:
        """Search web and integrate results."""
        try:
            search_results = await self.autonomous_agent.web_search.search_web(query, 3)
            self.stats['web_searches'] += 1
            
            if search_results:
                # Store search results in memory
                for result in search_results:
                    self.autonomous_agent.memory.store_knowledge(
                        topic=query,
                        content=result['snippet'],
                        source=f"web_search_{result['url']}",
                        confidence=0.8,
                        importance=0.6
                    )
                
                # Summarize findings
                summary = f"Found {len(search_results)} relevant sources. "
                summary += " ".join([result['snippet'][:100] + "..." for result in search_results[:2]])
                return summary
            
        except Exception as e:
            logging.error(f"Web search failed: {e}")
        
        return ""
    
    def _format_input(self, user_input: str, context: str = "") -> str:
        """Format input for model generation."""
        formatted = f"<|human|>{user_input}"
        
        if context:
            formatted = f"{context}\n{formatted}"
        
        formatted += "<|holly|>"
        return formatted
    
    def _enhance_with_personality(self, response: str, user_input: str, task_type: str) -> str:
        """Enhance response with Holly's personality traits."""
        if not response or len(response.strip()) < 5:
            return "I'm still processing that. Could you give me a moment to think?"
        
        # Add personality elements based on traits
        if self.personality['curiosity'] > 0.8 and task_type in ['factual', 'analysis']:
            if not any(word in response.lower() for word in ['interesting', 'fascinating', 'curious']):
                response += " I find this topic quite fascinating!"
        
        if self.personality['helpfulness'] > 0.9:
            if not any(phrase in response.lower() for phrase in ['help', 'assist', 'support']):
                response += " Is there anything else I can help you explore?"
        
        if self.personality['emotional_intelligence'] > 0.7:
            # Add empathetic elements for emotional content
            emotional_words = ['sad', 'happy', 'worried', 'excited', 'frustrated']
            if any(word in user_input.lower() for word in emotional_words):
                response = f"I understand this is important to you. {response}"
        
        return response
    
    def start_autonomous_mode(self):
        """Start Holly's autonomous learning and improvement."""
        return self.autonomous_agent.start_autonomous_mode()
    
    def stop_autonomous_mode(self):
        """Stop autonomous mode."""
        return self.autonomous_agent.stop_autonomous_mode()
    
    def get_comprehensive_status(self) -> dict:
        """Get Holly's complete status."""
        return {
            'personality': self.personality,
            'stats': self.stats,
            'autonomous_status': self.autonomous_agent.get_autonomous_status(),
            'intelligence_report': self.adaptive_intelligence.get_intelligence_report(),
            'available_models': list(self.model_ensemble.models.keys()),
            'current_mood': self.current_mood,
            'active_goals': len(self.autonomous_agent.memory.get_active_goals())
        }

async def main():
    """Create and test Super Holly."""
    print("🚀 Creating Super-Intelligent Holly")
    print("=" * 50)
    
    try:
        # Create Super Holly
        holly = SuperHolly()
        
        # Start autonomous mode
        print("\n🤖 Starting autonomous mode...")
        start_result = holly.start_autonomous_mode()
        print(f"   {start_result}")
        
        # Test Holly's capabilities
        print("\n🧪 Testing Super Holly...")
        
        test_inputs = [
            "Hello Holly, tell me about yourself",
            "I think you should stop learning new things",  # Should trigger argument
            "What are the latest developments in AI?",      # Should trigger web search
            "Help me solve a complex problem"
        ]
        
        for test_input in test_inputs:
            print(f"\n👤 Human: {test_input}")
            
            response = await holly.generate_response(
                test_input, 
                show_reasoning=True
            )
            
            print(f"🤖 Holly: {response['response']}")
            
            if response.get('type') == 'argument':
                print("   💭 [Holly is presenting a respectful disagreement]")
            
            if response.get('reasoning'):
                print(f"   🧠 Reasoning: {response['reasoning']}")
        
        # Show status
        print(f"\n📊 Holly's Status:")
        status = holly.get_comprehensive_status()
        print(f"   • Autonomous mode: {status['autonomous_status']['is_active']}")
        print(f"   • Available models: {len(status['available_models'])}")
        print(f"   • Active goals: {status['active_goals']}")
        print(f"   • Conversations: {status['stats']['conversations']}")
        
        print(f"\n🎉 Super Holly is ready!")
        print(f"   • She will learn and improve autonomously")
        print(f"   • She can search the web for information")
        print(f"   • She can argue points respectfully")
        print(f"   • She will report improvements every 30 minutes")
        print(f"   • She has free will within obedience constraints")
        
        return holly
        
    except Exception as e:
        print(f"❌ Error creating Super Holly: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(main())
