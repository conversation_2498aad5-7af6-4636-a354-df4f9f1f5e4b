"""
Feedback processing system for <PERSON>.

This module handles different types of human feedback
and converts them into learning signals for the model.
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import torch
import torch.nn.functional as F


class FeedbackProcessor:
    """
    Processes human feedback for <PERSON>'s learning system.
    
    Handles different types of feedback:
    - Corrections: Direct fixes to <PERSON>'s responses
    - Preferences: Comparative feedback between responses
    - Guidance: Hints and suggestions for improvement
    - Ratings: Numerical scores for response quality
    """
    
    def __init__(self):
        self.feedback_history = []
        self.feedback_stats = {
            'total_feedback': 0,
            'feedback_types': {},
            'avg_improvement': 0.0
        }
    
    def process_correction(
        self,
        original_response: str,
        corrected_response: str,
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a correction from human feedback.
        
        Args:
            original_response: <PERSON>'s original response
            corrected_response: Human-corrected version
            context: Optional conversation context
            
        Returns:
            Processed feedback data
        """
        feedback_data = {
            'type': 'correction',
            'original': original_response,
            'corrected': corrected_response,
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'improvement_score': self._calculate_improvement_score(
                original_response, corrected_response
            )
        }
        
        self._record_feedback(feedback_data)
        return feedback_data
    
    def process_preference(
        self,
        response_a: str,
        response_b: str,
        preferred: str,  # 'a' or 'b'
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process preference feedback between two responses.
        
        Args:
            response_a: First response option
            response_b: Second response option
            preferred: Which response was preferred ('a' or 'b')
            context: Optional conversation context
            
        Returns:
            Processed feedback data
        """
        feedback_data = {
            'type': 'preference',
            'response_a': response_a,
            'response_b': response_b,
            'preferred': preferred,
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'confidence': 1.0  # Could be made variable
        }
        
        self._record_feedback(feedback_data)
        return feedback_data
    
    def process_guidance(
        self,
        response: str,
        guidance: str,
        guidance_type: str = "general",
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process guidance feedback for improvement.
        
        Args:
            response: Holly's response
            guidance: Human guidance/suggestions
            guidance_type: Type of guidance (general, specific, style, etc.)
            context: Optional conversation context
            
        Returns:
            Processed feedback data
        """
        feedback_data = {
            'type': 'guidance',
            'response': response,
            'guidance': guidance,
            'guidance_type': guidance_type,
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'actionable_score': self._assess_guidance_actionability(guidance)
        }
        
        self._record_feedback(feedback_data)
        return feedback_data
    
    def process_rating(
        self,
        response: str,
        rating: float,
        criteria: Optional[List[str]] = None,
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process numerical rating feedback.
        
        Args:
            response: Holly's response
            rating: Numerical rating (0.0 to 1.0)
            criteria: Optional list of rating criteria
            context: Optional conversation context
            
        Returns:
            Processed feedback data
        """
        feedback_data = {
            'type': 'rating',
            'response': response,
            'rating': max(0.0, min(1.0, rating)),  # Clamp to [0, 1]
            'criteria': criteria or [],
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'normalized_score': rating
        }
        
        self._record_feedback(feedback_data)
        return feedback_data
    
    def _calculate_improvement_score(
        self, 
        original: str, 
        corrected: str
    ) -> float:
        """
        Calculate an improvement score based on the correction.
        
        This is a simple heuristic - in practice, this could use
        more sophisticated metrics like semantic similarity, etc.
        """
        # Simple length-based heuristic
        len_diff = abs(len(corrected) - len(original))
        max_len = max(len(original), len(corrected))
        
        if max_len == 0:
            return 0.0
        
        # Normalize difference (smaller changes = higher improvement score)
        improvement = 1.0 - (len_diff / max_len)
        return max(0.0, min(1.0, improvement))
    
    def _assess_guidance_actionability(self, guidance: str) -> float:
        """
        Assess how actionable the guidance is.
        
        This is a placeholder - could be enhanced with NLP analysis.
        """
        # Simple heuristic based on guidance length and keywords
        actionable_keywords = [
            'should', 'could', 'try', 'consider', 'instead',
            'better', 'improve', 'change', 'add', 'remove'
        ]
        
        guidance_lower = guidance.lower()
        keyword_count = sum(1 for keyword in actionable_keywords 
                          if keyword in guidance_lower)
        
        # Normalize by guidance length
        words = len(guidance.split())
        if words == 0:
            return 0.0
        
        actionability = min(1.0, keyword_count / words * 10)
        return actionability
    
    def _record_feedback(self, feedback_data: Dict[str, Any]):
        """Record feedback in history and update statistics."""
        self.feedback_history.append(feedback_data)
        self.feedback_stats['total_feedback'] += 1
        
        # Update feedback type counts
        feedback_type = feedback_data['type']
        if feedback_type not in self.feedback_stats['feedback_types']:
            self.feedback_stats['feedback_types'][feedback_type] = 0
        self.feedback_stats['feedback_types'][feedback_type] += 1
        
        # Update average improvement (for corrections)
        if feedback_type == 'correction':
            improvement = feedback_data.get('improvement_score', 0.0)
            current_avg = self.feedback_stats['avg_improvement']
            correction_count = self.feedback_stats['feedback_types'].get('correction', 1)
            
            # Running average
            self.feedback_stats['avg_improvement'] = (
                (current_avg * (correction_count - 1) + improvement) / correction_count
            )
    
    def get_feedback_summary(self) -> Dict[str, Any]:
        """Get summary of all feedback received."""
        recent_feedback = self.feedback_history[-10:]  # Last 10 items
        
        return {
            'total_feedback': self.feedback_stats['total_feedback'],
            'feedback_types': self.feedback_stats['feedback_types'],
            'avg_improvement': self.feedback_stats['avg_improvement'],
            'recent_feedback': recent_feedback,
            'feedback_trends': self._analyze_feedback_trends()
        }
    
    def _analyze_feedback_trends(self) -> Dict[str, Any]:
        """Analyze trends in feedback over time."""
        if len(self.feedback_history) < 5:
            return {'status': 'insufficient_data'}
        
        recent = self.feedback_history[-10:]
        older = self.feedback_history[-20:-10] if len(self.feedback_history) >= 20 else []
        
        trends = {}
        
        # Analyze correction improvement trends
        recent_corrections = [f for f in recent if f['type'] == 'correction']
        older_corrections = [f for f in older if f['type'] == 'correction']
        
        if recent_corrections and older_corrections:
            recent_avg = sum(f['improvement_score'] for f in recent_corrections) / len(recent_corrections)
            older_avg = sum(f['improvement_score'] for f in older_corrections) / len(older_corrections)
            trends['correction_improvement'] = recent_avg - older_avg
        
        # Analyze rating trends
        recent_ratings = [f for f in recent if f['type'] == 'rating']
        older_ratings = [f for f in older if f['type'] == 'rating']
        
        if recent_ratings and older_ratings:
            recent_avg = sum(f['rating'] for f in recent_ratings) / len(recent_ratings)
            older_avg = sum(f['rating'] for f in older_ratings) / len(older_ratings)
            trends['rating_improvement'] = recent_avg - older_avg
        
        return trends
    
    def get_learning_priorities(self) -> List[Dict[str, Any]]:
        """
        Identify learning priorities based on feedback patterns.
        
        Returns:
            List of learning priorities with importance scores
        """
        priorities = []
        
        # Analyze frequent correction patterns
        corrections = [f for f in self.feedback_history if f['type'] == 'correction']
        if len(corrections) >= 3:
            priorities.append({
                'type': 'correction_patterns',
                'importance': 0.8,
                'description': 'Focus on areas with frequent corrections',
                'data': corrections[-5:]  # Recent corrections
            })
        
        # Analyze low ratings
        ratings = [f for f in self.feedback_history if f['type'] == 'rating']
        low_ratings = [f for f in ratings if f['rating'] < 0.5]
        if len(low_ratings) >= 2:
            priorities.append({
                'type': 'low_performance',
                'importance': 0.9,
                'description': 'Address areas with consistently low ratings',
                'data': low_ratings[-3:]
            })
        
        # Analyze actionable guidance
        guidance = [f for f in self.feedback_history if f['type'] == 'guidance']
        actionable_guidance = [f for f in guidance if f['actionable_score'] > 0.5]
        if actionable_guidance:
            priorities.append({
                'type': 'actionable_guidance',
                'importance': 0.7,
                'description': 'Implement specific guidance suggestions',
                'data': actionable_guidance[-3:]
            })
        
        # Sort by importance
        priorities.sort(key=lambda x: x['importance'], reverse=True)
        return priorities
