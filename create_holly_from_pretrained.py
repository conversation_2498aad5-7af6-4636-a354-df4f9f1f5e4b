#!/usr/bin/env python3
"""
Create <PERSON> using pre-trained weights from Hugging Face.

This script loads a pre-trained language model and adapts it
for <PERSON>'s collaborative learning architecture, giving her
real language understanding from the start.
"""

import torch
import torch.nn as nn
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    GPT2LMHeadModel, GPT2Tokenizer,
    AutoConfig
)
import os
import json
from holly.core.model import HollyModel
from holly.core.tokenizer import HollyTokenizer
from holly.learning.trainer import CollaborativeTrainer
from datetime import datetime

def download_and_adapt_pretrained_model(model_name="microsoft/DialoGPT-small"):
    """
    Download and adapt a pre-trained model for <PERSON>.
    
    Args:
        model_name: Hugging Face model to use as base
    """
    print(f"🤗 Loading pre-trained model: {model_name}")
    print("=" * 60)
    
    try:
        # Load pre-trained model and tokenizer
        print("📥 Downloading model and tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)
        
        # Add special tokens for <PERSON>
        special_tokens = {
            "pad_token": "<pad>",
            "eos_token": "<eos>",
            "bos_token": "<bos>",
            "unk_token": "<unk>",
            "additional_special_tokens": [
                "<human>", "<holly>", "<think>", "</think>", 
                "<uncertain>", "<question>", "<improve>"
            ]
        }
        
        print("🔧 Adding Holly-specific tokens...")
        num_added_tokens = tokenizer.add_special_tokens(special_tokens)
        print(f"   Added {num_added_tokens} special tokens")
        
        # Resize model embeddings to accommodate new tokens
        model.resize_token_embeddings(len(tokenizer))
        
        print(f"✅ Model loaded successfully!")
        print(f"   Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   Vocabulary size: {len(tokenizer):,}")
        print(f"   Model type: {type(model).__name__}")
        
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Error loading {model_name}: {e}")
        print("🔄 Trying alternative model...")
        return download_and_adapt_pretrained_model("gpt2")

def create_holly_wrapper(pretrained_model, pretrained_tokenizer):
    """
    Create a Holly wrapper around the pre-trained model.
    """
    print("🌟 Creating Holly wrapper...")
    
    # Create Holly-compatible tokenizer
    holly_tokenizer = HollyTokenizer(
        vocab_size=len(pretrained_tokenizer),
        min_frequency=1
    )
    
    # Copy vocabulary from pre-trained tokenizer
    holly_tokenizer.token_to_id = pretrained_tokenizer.get_vocab()
    holly_tokenizer.id_to_token = {v: k for k, v in holly_tokenizer.token_to_id.items()}
    
    # Add Holly's special tokens if not present
    holly_special_tokens = {
        '<human>': len(holly_tokenizer.token_to_id),
        '<holly>': len(holly_tokenizer.token_to_id) + 1,
        '<think>': len(holly_tokenizer.token_to_id) + 2,
        '</think>': len(holly_tokenizer.token_to_id) + 3,
        '<uncertain>': len(holly_tokenizer.token_to_id) + 4,
        '<question>': len(holly_tokenizer.token_to_id) + 5,
        '<improve>': len(holly_tokenizer.token_to_id) + 6,
    }
    
    for token, token_id in holly_special_tokens.items():
        if token not in holly_tokenizer.token_to_id:
            holly_tokenizer.token_to_id[token] = token_id
            holly_tokenizer.id_to_token[token_id] = token
    
    print(f"📝 Holly tokenizer created with {len(holly_tokenizer.token_to_id)} tokens")
    
    # Create Holly model wrapper
    class HollyPretrainedWrapper(nn.Module):
        """Wrapper that makes pre-trained model compatible with Holly's interface."""
        
        def __init__(self, pretrained_model, config):
            super().__init__()
            self.pretrained_model = pretrained_model
            self.config = config
            
            # Holly-specific components
            self.reasoning_head = nn.Linear(config['d_model'], config['d_model'])
            self.confidence_head = nn.Linear(config['d_model'], 1)
            
            # Model metadata
            self.generation_count = 0
            self.learning_episodes = 0
            self.model_version = "1.0.0-pretrained"
            self.creation_time = datetime.now().isoformat()
            
            # Statistics
            self.stats = {
                'forward_passes': 0,
                'tokens_generated': 0,
                'learning_updates': 0,
                'reasoning_activations': 0,
                'confidence_scores': [],
                'attention_patterns': []
            }
        
        def forward(self, input_ids, attention_mask=None, return_reasoning=False, return_attention=False):
            """Forward pass compatible with Holly's interface."""
            self.stats['forward_passes'] += 1
            
            # Use pre-trained model
            outputs = self.pretrained_model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                output_attentions=return_attention,
                output_hidden_states=return_reasoning
            )
            
            result = {'logits': outputs.logits}
            
            # Add reasoning if requested
            if return_reasoning and hasattr(outputs, 'hidden_states') and outputs.hidden_states:
                hidden_states = outputs.hidden_states[-1]  # Last layer
                reasoning_states = self.reasoning_head(hidden_states)
                confidence_scores = torch.sigmoid(self.confidence_head(hidden_states))
                
                result['reasoning'] = reasoning_states
                result['confidence'] = confidence_scores
                
                self.stats['reasoning_activations'] += 1
                self.stats['confidence_scores'].extend(confidence_scores.mean(dim=(0,1)).tolist())
            
            # Add attention if requested
            if return_attention and hasattr(outputs, 'attentions') and outputs.attentions:
                result['attention_weights'] = outputs.attentions
            
            return result
        
        def generate(self, input_ids, max_length=50, temperature=0.8, top_k=50, top_p=0.9, 
                    do_sample=True, show_reasoning=False, **kwargs):
            """Generate text compatible with Holly's interface."""
            self.generation_count += 1
            
            # Use pre-trained model's generation
            with torch.no_grad():
                generated = self.pretrained_model.generate(
                    input_ids,
                    max_length=input_ids.size(1) + max_length,
                    temperature=temperature,
                    top_k=top_k,
                    top_p=top_p,
                    do_sample=do_sample,
                    pad_token_id=holly_tokenizer.token_to_id.get('<pad>', 0),
                    eos_token_id=holly_tokenizer.token_to_id.get('<eos>', 2),
                    **kwargs
                )
            
            # Extract new tokens
            new_tokens = generated[:, input_ids.size(1):]
            
            result = {
                'generated_ids': generated,
                'new_tokens': new_tokens,
                'generation_length': new_tokens.size(1)
            }
            
            # Add reasoning trace if requested
            if show_reasoning:
                result['reasoning_trace'] = [
                    {
                        'step': i,
                        'token_id': int(new_tokens[0, i]) if i < new_tokens.size(1) else -1,
                        'confidence': 0.8,  # Placeholder
                        'method': 'pretrained_generation'
                    }
                    for i in range(min(10, new_tokens.size(1)))
                ]
            
            self.stats['tokens_generated'] += new_tokens.size(1)
            return result
        
        def learn_from_feedback(self, input_ids, target_ids, feedback_type="correction", 
                               learning_rate=1e-4, apply_immediately=True):
            """Learn from feedback (simplified for pre-trained model)."""
            self.learning_episodes += 1
            
            # Calculate loss
            outputs = self.forward(input_ids)
            logits = outputs['logits']
            
            loss = nn.functional.cross_entropy(
                logits.view(-1, logits.size(-1)),
                target_ids.view(-1),
                ignore_index=-100
            )
            
            learning_stats = {
                'episode': self.learning_episodes,
                'feedback_type': feedback_type,
                'loss': float(loss),
                'learning_rate': learning_rate,
                'applied': apply_immediately,
                'timestamp': datetime.now().isoformat()
            }
            
            # For pre-trained models, we can do gradient updates but more carefully
            if apply_immediately:
                loss.backward()
                # Apply gradients with small learning rate
                with torch.no_grad():
                    for param in self.parameters():
                        if param.grad is not None:
                            param.data -= learning_rate * param.grad.data
                            param.grad.zero_()
            
            self.stats['learning_updates'] += 1
            return learning_stats
        
        def get_model_insights(self):
            """Get model insights compatible with Holly's interface."""
            return {
                'model_info': {
                    'version': self.model_version,
                    'creation_time': self.creation_time,
                    'config': self.config,
                    'parameter_count': sum(p.numel() for p in self.parameters()),
                    'base_model': 'pretrained',
                    'model_type': type(self.pretrained_model).__name__
                },
                'usage_stats': self.stats.copy(),
                'performance_metrics': {
                    'avg_confidence': sum(self.stats['confidence_scores'][-100:]) / max(len(self.stats['confidence_scores'][-100:]), 1),
                    'generation_efficiency': self.stats['tokens_generated'] / max(self.stats['forward_passes'], 1)
                }
            }
        
        def save_model(self, filepath):
            """Save the Holly model."""
            save_data = {
                'model_state_dict': self.state_dict(),
                'config': self.config,
                'model_version': self.model_version,
                'creation_time': self.creation_time,
                'generation_count': self.generation_count,
                'learning_episodes': self.learning_episodes,
                'stats': self.stats,
                'save_timestamp': datetime.now().isoformat(),
                'base_model_type': type(self.pretrained_model).__name__
            }
            
            torch.save(save_data, filepath)
            print(f"💾 Holly model saved to {filepath}")
        
        @classmethod
        def load_model(cls, filepath, device='cpu'):
            """Load Holly model (placeholder - would need full implementation)."""
            save_data = torch.load(filepath, map_location=device)
            # This would need more complex loading logic
            print(f"📂 Loading Holly model from {filepath}")
            return None  # Placeholder
    
    # Get model configuration
    if hasattr(pretrained_model, 'config'):
        model_config = pretrained_model.config
        d_model = getattr(model_config, 'hidden_size', getattr(model_config, 'n_embd', 768))
        n_layers = getattr(model_config, 'num_hidden_layers', getattr(model_config, 'n_layer', 12))
        n_heads = getattr(model_config, 'num_attention_heads', getattr(model_config, 'n_head', 12))
    else:
        d_model, n_layers, n_heads = 768, 12, 12
    
    holly_config = {
        'vocab_size': len(pretrained_tokenizer),
        'd_model': d_model,
        'n_layers': n_layers,
        'n_heads': n_heads,
        'd_ff': d_model * 4,
        'max_seq_length': 1024,
        'dropout': 0.1,
        'activation': 'gelu',
        'use_gating': False,
        'adaptive_norm': True,
        'reasoning_mode': True
    }
    
    # Create Holly wrapper
    holly_model = HollyPretrainedWrapper(pretrained_model, holly_config)
    
    print(f"✅ Holly wrapper created!")
    print(f"   Parameters: {sum(p.numel() for p in holly_model.parameters()):,}")
    print(f"   Model dimension: {d_model}")
    print(f"   Layers: {n_layers}")
    print(f"   Attention heads: {n_heads}")
    
    return holly_model, holly_tokenizer

def test_pretrained_holly(model, tokenizer):
    """Test the pre-trained Holly."""
    print("\n🧪 Testing Pre-trained Holly...")
    
    test_inputs = [
        "Hello, what's your name?",
        "How are you today?",
        "What can you help me with?",
        "Tell me something interesting",
        "What do you think about AI?"
    ]
    
    model.eval()
    
    for test_input in test_inputs:
        print(f"\n👤 Human: {test_input}")
        
        # Encode input
        input_text = f"<human>{test_input}<eos><holly>"
        
        # Use the pre-trained tokenizer for encoding
        if hasattr(tokenizer, 'encode'):
            input_ids = torch.tensor([tokenizer.encode(input_text)])
        else:
            # Fallback encoding
            tokens = input_text.split()
            input_ids = torch.tensor([[tokenizer.token_to_id.get(token, 1) for token in tokens]])
        
        # Generate response
        try:
            result = model.generate(
                input_ids,
                max_length=50,
                temperature=0.8,
                top_k=50,
                top_p=0.9,
                do_sample=True
            )
            
            # Decode response
            new_tokens = result['new_tokens'][0].tolist()
            
            # Use pre-trained tokenizer for decoding
            if hasattr(tokenizer, 'decode'):
                response = tokenizer.decode(new_tokens, skip_special_tokens=True)
            else:
                # Fallback decoding
                response = ' '.join([tokenizer.id_to_token.get(token_id, '<unk>') for token_id in new_tokens])
            
            response = response.replace("<eos>", "").strip()
            print(f"🤖 Holly: {response}")
            
        except Exception as e:
            print(f"🤖 Holly: [Generation error: {e}]")

def main():
    """Main function to create pre-trained Holly."""
    print("🌟 Creating Holly from Pre-trained Weights")
    print("=" * 50)
    
    # Try different models in order of preference
    model_options = [
        "microsoft/DialoGPT-small",  # Conversation-focused
        "gpt2",                      # General language model
        "distilgpt2",               # Smaller, faster
    ]
    
    model, tokenizer = None, None
    
    for model_name in model_options:
        try:
            print(f"\n🔄 Trying {model_name}...")
            model, tokenizer = download_and_adapt_pretrained_model(model_name)
            if model is not None:
                print(f"✅ Successfully loaded {model_name}")
                break
        except Exception as e:
            print(f"❌ Failed to load {model_name}: {e}")
            continue
    
    if model is None:
        print("❌ Could not load any pre-trained model")
        return None, None
    
    # Create Holly wrapper
    holly_model, holly_tokenizer = create_holly_wrapper(model, tokenizer)
    
    # Test the model
    test_pretrained_holly(holly_model, holly_tokenizer)
    
    # Save Holly
    model_path = "models/holly_pretrained.pt"
    tokenizer_path = "models/holly_pretrained_tokenizer.json"
    
    os.makedirs("models", exist_ok=True)
    
    holly_model.save_model(model_path)
    holly_tokenizer.save_vocabulary(tokenizer_path)
    
    print(f"\n🎉 Pre-trained Holly created successfully!")
    print(f"   Model: {model_path}")
    print(f"   Tokenizer: {tokenizer_path}")
    print(f"   Parameters: {sum(p.numel() for p in holly_model.parameters()):,}")
    print(f"   Base model: {type(model).__name__}")
    
    print(f"\n🚀 To use Pre-trained Holly:")
    print(f"   python holly/server.py --model-path {model_path}")
    
    return model_path, tokenizer_path

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Error creating pre-trained Holly: {e}")
        import traceback
        traceback.print_exc()
