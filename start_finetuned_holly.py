#!/usr/bin/env python3
"""
Start <PERSON> with the fine-tuned model.

This script loads the fine-tuned Holly model and starts the web interface
with proper language capabilities.
"""

import torch
import os
import sys
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from holly.interface.server import HollyServer
from holly.core.tokenizer import HollyTokenizer

class FinetunedHollyWrapper:
    """Wrapper to make fine-tuned model compatible with <PERSON>'s interface."""
    
    def __init__(self, model, tokenizer):
        self.model = model
        self.hf_tokenizer = tokenizer
        
        # Create Holly-compatible tokenizer
        self.holly_tokenizer = HollyTokenizer()
        self.holly_tokenizer.token_to_id = tokenizer.get_vocab()
        self.holly_tokenizer.id_to_token = {v: k for k, v in self.holly_tokenizer.token_to_id.items()}
        
        # Model metadata
        self.generation_count = 0
        self.learning_episodes = 0
        self.model_version = "1.0.0-finetuned"
        
        # Statistics
        self.stats = {
            'forward_passes': 0,
            'tokens_generated': 0,
            'learning_updates': 0,
            'reasoning_activations': 0,
            'confidence_scores': [],
            'attention_patterns': []
        }
        
        # Configuration
        self.config = {
            'vocab_size': len(tokenizer),
            'd_model': 768,
            'n_layers': 12,
            'n_heads': 12,
            'd_ff': 3072,
            'max_seq_length': 1024,
            'dropout': 0.1,
            'activation': 'gelu',
            'use_gating': False,
            'adaptive_norm': True,
            'reasoning_mode': True
        }
    
    def encode(self, text):
        """Encode text using the fine-tuned tokenizer."""
        return self.hf_tokenizer.encode(text)
    
    def decode(self, token_ids, skip_special_tokens=True):
        """Decode token IDs using the fine-tuned tokenizer."""
        return self.hf_tokenizer.decode(token_ids, skip_special_tokens=skip_special_tokens)
    
    def get_vocab_size(self):
        """Get vocabulary size."""
        return len(self.hf_tokenizer)
    
    def generate(self, input_ids, max_length=100, temperature=0.8, top_k=50, top_p=0.9, 
                do_sample=True, show_reasoning=False, **kwargs):
        """Generate text using the fine-tuned model."""
        self.generation_count += 1
        self.stats['forward_passes'] += 1
        
        # Convert input if needed
        if isinstance(input_ids, list):
            input_ids = torch.tensor([input_ids])
        elif len(input_ids.shape) == 1:
            input_ids = input_ids.unsqueeze(0)
        
        # Generate with fine-tuned model
        with torch.no_grad():
            output = self.model.generate(
                input_ids,
                max_length=input_ids.size(1) + max_length,
                temperature=temperature,
                top_k=top_k,
                top_p=top_p,
                do_sample=do_sample,
                pad_token_id=self.hf_tokenizer.pad_token_id,
                eos_token_id=self.hf_tokenizer.eos_token_id,
                no_repeat_ngram_size=3,  # Prevent repetition
                **kwargs
            )
        
        # Extract new tokens
        new_tokens = output[:, input_ids.size(1):]
        
        result = {
            'generated_ids': output,
            'new_tokens': new_tokens,
            'generation_length': new_tokens.size(1)
        }
        
        # Add reasoning trace if requested
        if show_reasoning:
            result['reasoning_trace'] = [
                {
                    'step': i,
                    'token_id': int(new_tokens[0, i]) if i < new_tokens.size(1) else -1,
                    'confidence': 0.8,
                    'method': 'finetuned_generation'
                }
                for i in range(min(10, new_tokens.size(1)))
            ]
        
        self.stats['tokens_generated'] += new_tokens.size(1)
        return result
    
    def forward(self, input_ids, attention_mask=None, return_reasoning=False, return_attention=False):
        """Forward pass for compatibility."""
        self.stats['forward_passes'] += 1
        
        outputs = self.model(input_ids, attention_mask=attention_mask)
        
        result = {'logits': outputs.logits}
        
        if return_reasoning:
            # Simulate reasoning for compatibility
            result['reasoning'] = outputs.logits  # Placeholder
            result['confidence'] = torch.sigmoid(outputs.logits.mean(dim=-1, keepdim=True))
            self.stats['reasoning_activations'] += 1
        
        return result
    
    def learn_from_feedback(self, input_ids, target_ids, feedback_type="correction", 
                           learning_rate=1e-5, apply_immediately=True):
        """Learn from feedback with fine-tuned model."""
        self.learning_episodes += 1
        
        # Calculate loss
        outputs = self.forward(input_ids)
        logits = outputs['logits']
        
        loss = torch.nn.functional.cross_entropy(
            logits.view(-1, logits.size(-1)),
            target_ids.view(-1),
            ignore_index=-100
        )
        
        learning_stats = {
            'episode': self.learning_episodes,
            'feedback_type': feedback_type,
            'loss': float(loss),
            'learning_rate': learning_rate,
            'applied': apply_immediately,
            'timestamp': torch.datetime.now().isoformat() if hasattr(torch, 'datetime') else 'unknown'
        }
        
        # Apply gradients if requested (with very small learning rate for fine-tuned model)
        if apply_immediately:
            loss.backward()
            with torch.no_grad():
                for param in self.model.parameters():
                    if param.grad is not None:
                        param.data -= learning_rate * param.grad.data
                        param.grad.zero_()
        
        self.stats['learning_updates'] += 1
        return learning_stats
    
    def get_model_insights(self):
        """Get model insights."""
        return {
            'model_info': {
                'version': self.model_version,
                'config': self.config,
                'parameter_count': sum(p.numel() for p in self.model.parameters()),
                'base_model': 'microsoft/DialoGPT-small (fine-tuned)'
            },
            'usage_stats': self.stats.copy(),
            'performance_metrics': {
                'avg_confidence': 0.8,  # Placeholder
                'generation_efficiency': self.stats['tokens_generated'] / max(self.stats['forward_passes'], 1)
            }
        }
    
    def save_model(self, filepath):
        """Save model (placeholder)."""
        print(f"💾 Saving fine-tuned Holly to {filepath}")
        # The actual model is already saved in Hugging Face format

def load_finetuned_holly():
    """Load the fine-tuned Holly model."""
    print("🌟 Loading Fine-tuned Holly")
    print("=" * 40)
    
    model_dir = "models/holly_finetuned"
    
    if not os.path.exists(model_dir):
        print(f"❌ Fine-tuned model not found at {model_dir}")
        print("💡 Please run 'python finetune_holly.py' first")
        return None, None
    
    try:
        print("📥 Loading fine-tuned model and tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_dir)
        model = AutoModelForCausalLM.from_pretrained(model_dir)
        
        # Ensure pad token is set
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print(f"✅ Fine-tuned Holly loaded successfully!")
        print(f"   Parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   Vocabulary: {len(tokenizer):,}")
        
        # Create wrapper
        holly_wrapper = FinetunedHollyWrapper(model, tokenizer)
        
        return holly_wrapper, holly_wrapper.holly_tokenizer
        
    except Exception as e:
        print(f"❌ Error loading fine-tuned model: {e}")
        return None, None

def test_finetuned_holly(model, tokenizer):
    """Test the fine-tuned Holly before starting server."""
    print("\n🧪 Testing Fine-tuned Holly...")
    
    test_inputs = [
        "Hello Holly",
        "What is your name?",
        "How can you help me?"
    ]
    
    for test_input in test_inputs:
        print(f"\n👤 Human: {test_input}")
        
        # Format input for Holly
        input_text = f"<|human|>{test_input}<|holly|>"
        input_ids = torch.tensor([model.encode(input_text)])
        
        # Generate response
        result = model.generate(
            input_ids,
            max_length=50,
            temperature=0.7,
            top_k=50,
            top_p=0.9,
            do_sample=True
        )
        
        # Decode response
        generated_text = model.decode(result['generated_ids'][0].tolist())
        
        # Extract Holly's response
        if "<|holly|>" in generated_text:
            response = generated_text.split("<|holly|>")[-1].strip()
            # Clean up response
            response = response.replace("<|endoftext|>", "").strip()
            if not response:
                response = "I'm still learning to respond properly."
        else:
            response = "I'm having trouble generating a response."
        
        print(f"🤖 Holly: {response}")

def main():
    """Main function to start fine-tuned Holly."""
    # Load fine-tuned Holly
    model, tokenizer = load_finetuned_holly()
    
    if model is None:
        print("❌ Could not load fine-tuned Holly")
        return
    
    # Test the model
    test_finetuned_holly(model, tokenizer)
    
    print("\n🚀 Starting Holly web interface...")
    
    try:
        # Create and start server with fine-tuned model
        server = HollyServer(
            model_path=None,  # We're providing the model directly
            host="localhost",
            port=8000,
            debug=True
        )
        
        # Replace the server's model and tokenizer with our fine-tuned versions
        server.model = model
        server.tokenizer = tokenizer
        
        # Update the chat interface
        if hasattr(server, 'chat_interface'):
            server.chat_interface.model = model
            server.chat_interface.tokenizer = tokenizer
        
        print("✅ Fine-tuned Holly is ready!")
        print("🌐 Open http://localhost:8000 in your browser")
        print("💡 Holly now has much better language understanding!")
        
        # Run the server
        server.run()
        
    except KeyboardInterrupt:
        print("\n👋 Fine-tuned Holly stopped. Goodbye!")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
