"""
Holly - Main model implementation.

This module contains the core HollyModel class that brings together
all components into a complete language model with self-modification
and collaborative learning capabilities.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
import json
import os
from datetime import datetime

from holly.core.embeddings import HollyEmbeddings
from holly.core.layers import TransformerBlock, AdaptiveLayerNorm
from holly.core.tokenizer import HollyTokenizer


class HollyModel(nn.Module):
    """
    Holly - Experimental Collaborative AI Model
    
    A transformer-based language model designed for:
    - Transparent reasoning and decision-making
    - Self-modification and continuous learning
    - Collaborative human-AI development
    - Incremental knowledge acquisition
    """
    
    def __init__(
        self,
        vocab_size: int = 8192,
        d_model: int = 512,
        n_layers: int = 6,
        n_heads: int = 8,
        d_ff: Optional[int] = None,
        max_seq_length: int = 2048,
        dropout: float = 0.1,
        activation: str = "gelu",
        use_gating: bool = False,
        adaptive_norm: bool = True,
        reasoning_mode: bool = True
    ):
        super().__init__()
        
        # Model configuration
        self.config = {
            'vocab_size': vocab_size,
            'd_model': d_model,
            'n_layers': n_layers,
            'n_heads': n_heads,
            'd_ff': d_ff or 4 * d_model,
            'max_seq_length': max_seq_length,
            'dropout': dropout,
            'activation': activation,
            'use_gating': use_gating,
            'adaptive_norm': adaptive_norm,
            'reasoning_mode': reasoning_mode
        }
        
        # Core components
        self.embeddings = HollyEmbeddings(
            vocab_size=vocab_size,
            d_model=d_model,
            max_seq_length=max_seq_length,
            dropout=dropout
        )
        
        # Transformer layers
        self.layers = nn.ModuleList([
            TransformerBlock(
                d_model=d_model,
                n_heads=n_heads,
                d_ff=d_ff,
                dropout=dropout,
                activation=activation,
                use_gating=use_gating,
                adaptive_norm=adaptive_norm
            )
            for _ in range(n_layers)
        ])
        
        # Output layers
        self.final_norm = AdaptiveLayerNorm(d_model, adaptive=adaptive_norm)
        self.output_projection = nn.Linear(d_model, vocab_size, bias=False)
        
        # Reasoning components (if enabled)
        if reasoning_mode:
            self.reasoning_head = nn.Linear(d_model, d_model)
            self.confidence_head = nn.Linear(d_model, 1)
        
        # Model state tracking
        self.generation_count = 0
        self.learning_episodes = 0
        self.model_version = "0.1.0"
        self.creation_time = datetime.now().isoformat()
        
        # Performance and behavior statistics
        self.stats = {
            'forward_passes': 0,
            'tokens_generated': 0,
            'learning_updates': 0,
            'reasoning_activations': 0,
            'confidence_scores': [],
            'attention_patterns': []
        }
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights with careful scaling."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.xavier_uniform_(module.weight)
    
    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        return_reasoning: bool = False,
        return_attention: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through Holly.
        
        Args:
            input_ids: Token IDs [batch_size, seq_len]
            attention_mask: Optional attention mask
            return_reasoning: Whether to return reasoning information
            return_attention: Whether to return attention weights
            
        Returns:
            Dictionary containing:
            - logits: Output logits [batch_size, seq_len, vocab_size]
            - reasoning: Optional reasoning information
            - attention_weights: Optional attention weights
            - confidence: Confidence scores
        """
        self.stats['forward_passes'] += 1
        batch_size, seq_len = input_ids.size()
        
        # Embeddings
        hidden_states = self.embeddings(input_ids)
        
        # Create causal mask if not provided
        if attention_mask is None:
            attention_mask = self._create_causal_mask(seq_len, input_ids.device)
        
        # Store attention weights if requested
        all_attention_weights = [] if return_attention else None
        
        # Pass through transformer layers
        for i, layer in enumerate(self.layers):
            hidden_states, attention_weights = layer(
                hidden_states, 
                mask=attention_mask,
                return_attention=return_attention
            )
            
            if return_attention and attention_weights is not None:
                all_attention_weights.append(attention_weights)
        
        # Final normalization
        hidden_states = self.final_norm(hidden_states)
        
        # Output projection
        logits = self.output_projection(hidden_states)
        
        # Prepare output dictionary
        output = {'logits': logits}
        
        # Add reasoning information if requested
        if return_reasoning and hasattr(self, 'reasoning_head'):
            reasoning_states = self.reasoning_head(hidden_states)
            confidence_scores = torch.sigmoid(self.confidence_head(hidden_states))
            
            output['reasoning'] = reasoning_states
            output['confidence'] = confidence_scores
            
            # Update statistics
            self.stats['reasoning_activations'] += 1
            self.stats['confidence_scores'].extend(confidence_scores.mean(dim=(0,1)).tolist())
        
        # Add attention weights if requested
        if return_attention and all_attention_weights:
            output['attention_weights'] = all_attention_weights
            
            # Store attention patterns for analysis
            avg_attention = torch.stack(all_attention_weights).mean(dim=0)
            self.stats['attention_patterns'].append({
                'entropy': self._calculate_attention_entropy(avg_attention),
                'max_attention': float(avg_attention.max()),
                'attention_spread': float(avg_attention.std())
            })
        
        return output

    def _create_causal_mask(self, seq_len: int, device: torch.device) -> torch.Tensor:
        """Create causal attention mask."""
        mask = torch.tril(torch.ones(seq_len, seq_len, device=device))
        return mask.unsqueeze(0).unsqueeze(0)  # [1, 1, seq_len, seq_len]

    def _calculate_attention_entropy(self, attention_weights: torch.Tensor) -> float:
        """Calculate entropy of attention weights."""
        # Average across batch and heads
        avg_attention = attention_weights.mean(dim=(0, 1))
        # Add small epsilon to avoid log(0)
        eps = 1e-8
        entropy = -torch.sum(avg_attention * torch.log(avg_attention + eps))
        return float(entropy)

    def generate(
        self,
        input_ids: torch.Tensor,
        max_length: int = 100,
        temperature: float = 1.0,
        top_k: int = 50,
        top_p: float = 0.9,
        do_sample: bool = True,
        show_reasoning: bool = False
    ) -> Dict[str, Any]:
        """
        Generate text with Holly.

        Args:
            input_ids: Input token IDs [1, seq_len]
            max_length: Maximum generation length
            temperature: Sampling temperature
            top_k: Top-k sampling parameter
            top_p: Top-p (nucleus) sampling parameter
            do_sample: Whether to sample or use greedy decoding
            show_reasoning: Whether to include reasoning information

        Returns:
            Dictionary with generated tokens, text, and optional reasoning
        """
        self.eval()
        self.generation_count += 1

        generated_ids = input_ids.clone()
        reasoning_trace = [] if show_reasoning else None

        with torch.no_grad():
            for step in range(max_length):
                # Forward pass
                outputs = self.forward(
                    generated_ids,
                    return_reasoning=show_reasoning,
                    return_attention=show_reasoning
                )

                # Get next token logits
                next_token_logits = outputs['logits'][:, -1, :]

                # Apply temperature
                if temperature != 1.0:
                    next_token_logits = next_token_logits / temperature

                # Sample next token
                if do_sample:
                    # Apply top-k and top-p filtering
                    filtered_logits = self._filter_logits(next_token_logits, top_k, top_p)
                    probs = F.softmax(filtered_logits, dim=-1)
                    next_token = torch.multinomial(probs, num_samples=1)
                else:
                    next_token = torch.argmax(next_token_logits, dim=-1, keepdim=True)

                # Add to sequence
                generated_ids = torch.cat([generated_ids, next_token], dim=-1)

                # Store reasoning information
                if show_reasoning and reasoning_trace is not None:
                    step_info = {
                        'step': step,
                        'token_id': int(next_token[0]),
                        'confidence': float(outputs.get('confidence', torch.tensor(0.5))[:, -1].mean()),
                        'top_logits': next_token_logits.topk(5).values.tolist(),
                        'attention_summary': self._summarize_attention(outputs.get('attention_weights'))
                    }
                    reasoning_trace.append(step_info)

                # Check for end token
                if next_token.item() == 3:  # <eos> token
                    break

        self.stats['tokens_generated'] += generated_ids.size(1) - input_ids.size(1)

        result = {
            'generated_ids': generated_ids,
            'new_tokens': generated_ids[:, input_ids.size(1):],
            'generation_length': generated_ids.size(1) - input_ids.size(1)
        }

        if reasoning_trace:
            result['reasoning_trace'] = reasoning_trace

        return result

    def _filter_logits(self, logits: torch.Tensor, top_k: int, top_p: float) -> torch.Tensor:
        """Apply top-k and top-p filtering to logits."""
        # Top-k filtering
        if top_k > 0:
            top_k = min(top_k, logits.size(-1))
            indices_to_remove = logits < torch.topk(logits, top_k)[0][..., -1, None]
            logits[indices_to_remove] = float('-inf')

        # Top-p (nucleus) filtering
        if top_p < 1.0:
            sorted_logits, sorted_indices = torch.sort(logits, descending=True)
            cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)

            # Remove tokens with cumulative probability above the threshold
            sorted_indices_to_remove = cumulative_probs > top_p
            sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
            sorted_indices_to_remove[..., 0] = 0

            indices_to_remove = sorted_indices_to_remove.scatter(
                dim=-1, index=sorted_indices, src=sorted_indices_to_remove
            )
            logits[indices_to_remove] = float('-inf')

        return logits

    def _summarize_attention(self, attention_weights: Optional[List[torch.Tensor]]) -> Dict[str, Any]:
        """Summarize attention weights for reasoning trace."""
        if not attention_weights:
            return {}

        # Average across all layers and heads
        all_attention = torch.stack(attention_weights)  # [n_layers, batch, n_heads, seq_len, seq_len]
        avg_attention = all_attention.mean(dim=(0, 1, 2))  # [seq_len, seq_len]

        return {
            'max_attention_position': int(avg_attention[-1].argmax()),
            'attention_entropy': float(self._calculate_attention_entropy(all_attention)),
            'self_attention_strength': float(avg_attention[-1, -1])
        }

    def learn_from_feedback(
        self,
        input_ids: torch.Tensor,
        target_ids: torch.Tensor,
        feedback_type: str = "correction",
        learning_rate: float = 1e-4,
        apply_immediately: bool = True
    ) -> Dict[str, Any]:
        """
        Learn from human feedback.

        Args:
            input_ids: Input sequence
            target_ids: Target/corrected sequence
            feedback_type: Type of feedback ("correction", "preference", "guidance")
            learning_rate: Learning rate for this update
            apply_immediately: Whether to apply updates immediately

        Returns:
            Learning statistics and results
        """
        self.train()
        self.learning_episodes += 1

        # Forward pass to get current predictions
        outputs = self.forward(input_ids, return_reasoning=True)
        logits = outputs['logits']

        # Calculate loss
        loss = F.cross_entropy(
            logits.view(-1, logits.size(-1)),
            target_ids.view(-1),
            ignore_index=-100
        )

        # Calculate gradients
        loss.backward()

        # Store learning statistics
        learning_stats = {
            'episode': self.learning_episodes,
            'feedback_type': feedback_type,
            'loss': float(loss),
            'learning_rate': learning_rate,
            'gradient_norm': self._calculate_gradient_norm(),
            'confidence_before': float(outputs.get('confidence', torch.tensor(0.5)).mean()),
            'timestamp': datetime.now().isoformat()
        }

        # Apply updates if requested
        if apply_immediately:
            self._apply_gradients(learning_rate)
            learning_stats['applied'] = True
        else:
            learning_stats['applied'] = False

        self.stats['learning_updates'] += 1

        return learning_stats

    def _calculate_gradient_norm(self) -> float:
        """Calculate the norm of current gradients."""
        total_norm = 0.0
        for param in self.parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        return total_norm ** 0.5

    def _apply_gradients(self, learning_rate: float):
        """Apply gradients with specified learning rate."""
        with torch.no_grad():
            for param in self.parameters():
                if param.grad is not None:
                    param.data -= learning_rate * param.grad.data
                    param.grad.zero_()

    def propose_architecture_change(self, change_type: str) -> Dict[str, Any]:
        """
        Propose changes to Holly's own architecture.

        This is a placeholder for self-modification capabilities.
        In practice, this would analyze performance and suggest improvements.
        """
        proposals = {
            'add_layer': {
                'description': 'Add an additional transformer layer',
                'rationale': 'Increase model capacity for complex reasoning',
                'risk_level': 'medium',
                'estimated_improvement': '5-10% on complex tasks'
            },
            'adjust_attention': {
                'description': 'Modify attention head configuration',
                'rationale': 'Optimize attention patterns based on usage',
                'risk_level': 'low',
                'estimated_improvement': '2-5% efficiency gain'
            },
            'expand_vocabulary': {
                'description': 'Increase vocabulary size',
                'rationale': 'Better handle domain-specific terms',
                'risk_level': 'low',
                'estimated_improvement': 'Better domain coverage'
            }
        }

        # Analyze current performance to make informed suggestions
        current_stats = self.get_model_insights()

        if change_type in proposals:
            proposal = proposals[change_type].copy()
            proposal['current_stats'] = current_stats
            proposal['timestamp'] = datetime.now().isoformat()
            return proposal
        else:
            return {
                'error': f'Unknown change type: {change_type}',
                'available_types': list(proposals.keys())
            }

    def get_model_insights(self) -> Dict[str, Any]:
        """Get comprehensive insights about Holly's current state."""
        insights = {
            'model_info': {
                'version': self.model_version,
                'creation_time': self.creation_time,
                'config': self.config,
                'parameter_count': sum(p.numel() for p in self.parameters()),
                'trainable_parameters': sum(p.numel() for p in self.parameters() if p.requires_grad)
            },
            'usage_stats': self.stats.copy(),
            'performance_metrics': {
                'avg_confidence': sum(self.stats['confidence_scores'][-100:]) / max(len(self.stats['confidence_scores'][-100:]), 1),
                'attention_entropy_trend': [p['entropy'] for p in self.stats['attention_patterns'][-10:]],
                'generation_efficiency': self.stats['tokens_generated'] / max(self.stats['forward_passes'], 1)
            },
            'component_insights': {
                'embeddings': self.embeddings.get_embedding_insights(),
                'layer_stats': [layer.get_transparency_info() for layer in self.layers]
            }
        }

        return insights

    def save_model(self, filepath: str, include_stats: bool = True):
        """
        Save Holly's current state.

        Args:
            filepath: Path to save the model
            include_stats: Whether to include usage statistics
        """
        save_data = {
            'model_state_dict': self.state_dict(),
            'config': self.config,
            'model_version': self.model_version,
            'creation_time': self.creation_time,
            'generation_count': self.generation_count,
            'learning_episodes': self.learning_episodes,
            'save_timestamp': datetime.now().isoformat()
        }

        if include_stats:
            save_data['stats'] = self.stats

        torch.save(save_data, filepath)

    @classmethod
    def load_model(cls, filepath: str, device: str = 'cpu') -> 'HollyModel':
        """
        Load Holly from saved state.

        Args:
            filepath: Path to saved model
            device: Device to load model on

        Returns:
            Loaded HollyModel instance
        """
        save_data = torch.load(filepath, map_location=device)

        # Create model with saved configuration
        model = cls(**save_data['config'])

        # Load state
        model.load_state_dict(save_data['model_state_dict'])

        # Restore metadata
        model.model_version = save_data.get('model_version', '0.1.0')
        model.creation_time = save_data.get('creation_time', datetime.now().isoformat())
        model.generation_count = save_data.get('generation_count', 0)
        model.learning_episodes = save_data.get('learning_episodes', 0)

        # Restore statistics if available
        if 'stats' in save_data:
            model.stats = save_data['stats']

        return model

    def create_checkpoint(self, checkpoint_dir: str, reason: str = "periodic"):
        """
        Create a versioned checkpoint of Holly's current state.

        Args:
            checkpoint_dir: Directory to save checkpoints
            reason: Reason for creating checkpoint
        """
        os.makedirs(checkpoint_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_name = f"holly_v{self.model_version}_{timestamp}_{reason}.pt"
        checkpoint_path = os.path.join(checkpoint_dir, checkpoint_name)

        # Save model with additional checkpoint metadata
        checkpoint_data = {
            'model_state_dict': self.state_dict(),
            'config': self.config,
            'model_version': self.model_version,
            'creation_time': self.creation_time,
            'generation_count': self.generation_count,
            'learning_episodes': self.learning_episodes,
            'stats': self.stats,
            'checkpoint_reason': reason,
            'checkpoint_timestamp': datetime.now().isoformat(),
            'insights': self.get_model_insights()
        }

        torch.save(checkpoint_data, checkpoint_path)

        # Also save a human-readable summary
        summary_path = checkpoint_path.replace('.pt', '_summary.json')
        with open(summary_path, 'w') as f:
            json.dump({
                'checkpoint_info': {
                    'filename': checkpoint_name,
                    'reason': reason,
                    'timestamp': checkpoint_data['checkpoint_timestamp'],
                    'model_version': self.model_version
                },
                'model_summary': {
                    'parameters': sum(p.numel() for p in self.parameters()),
                    'layers': self.config['n_layers'],
                    'vocab_size': self.config['vocab_size'],
                    'generation_count': self.generation_count,
                    'learning_episodes': self.learning_episodes
                },
                'performance_summary': {
                    'forward_passes': self.stats['forward_passes'],
                    'tokens_generated': self.stats['tokens_generated'],
                    'avg_confidence': sum(self.stats['confidence_scores'][-100:]) / max(len(self.stats['confidence_scores'][-100:]), 1)
                }
            }, f, indent=2)

        return checkpoint_path

    def __str__(self) -> str:
        """String representation of Holly."""
        return f"""Holly Model v{self.model_version}
Created: {self.creation_time}
Parameters: {sum(p.numel() for p in self.parameters()):,}
Layers: {self.config['n_layers']}
Vocab Size: {self.config['vocab_size']}
Generations: {self.generation_count}
Learning Episodes: {self.learning_episodes}"""
