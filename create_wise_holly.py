#!/usr/bin/env python3
"""
Create a wise, articulate <PERSON> with proper language capabilities.

This script creates a much larger, better-initialized Holly model
with extensive language knowledge and communication skills.
"""

import torch
import torch.nn as nn
import numpy as np
from holly.core.model import <PERSON>Model
from holly.core.tokenizer import HollyTokenizer
from holly.learning.trainer import CollaborativeTrainer
import os
import json
import re
from typing import List, Dict, Any

def create_comprehensive_vocabulary():
    """Create a comprehensive vocabulary for <PERSON>."""
    print("📚 Building comprehensive vocabulary...")
    
    # Extensive vocabulary covering multiple domains
    vocabulary_sets = {
        'basic_words': [
            # Core communication
            'hello', 'hi', 'hey', 'goodbye', 'bye', 'thanks', 'thank', 'you', 'please',
            'yes', 'no', 'maybe', 'sure', 'okay', 'ok', 'good', 'bad', 'great', 'nice',
            'sorry', 'excuse', 'pardon', 'welcome', 'help', 'assist', 'support',
            
            # Pronouns and grammar
            'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
            'my', 'your', 'his', 'her', 'its', 'our', 'their', 'mine', 'yours',
            'this', 'that', 'these', 'those', 'here', 'there', 'where', 'when', 'what',
            'who', 'why', 'how', 'which', 'whose', 'whom',
            
            # Verbs
            'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'can', 'may', 'might',
            'go', 'come', 'see', 'look', 'get', 'give', 'take', 'make', 'know', 'think',
            'say', 'tell', 'ask', 'answer', 'speak', 'talk', 'listen', 'hear', 'understand',
            'learn', 'teach', 'study', 'read', 'write', 'work', 'play', 'run', 'walk',
            'feel', 'want', 'need', 'like', 'love', 'hate', 'prefer', 'choose', 'decide',
        ],
        
        'intellectual_words': [
            # Thinking and reasoning
            'analyze', 'consider', 'contemplate', 'deliberate', 'evaluate', 'examine',
            'explore', 'investigate', 'ponder', 'reflect', 'reason', 'theorize',
            'hypothesis', 'conclusion', 'evidence', 'logic', 'rational', 'intuition',
            'wisdom', 'knowledge', 'understanding', 'insight', 'perspective', 'viewpoint',
            'philosophy', 'principle', 'concept', 'idea', 'thought', 'notion',
            'intelligent', 'clever', 'brilliant', 'wise', 'thoughtful', 'insightful',
            
            # Communication and expression
            'articulate', 'eloquent', 'express', 'communicate', 'convey', 'explain',
            'describe', 'illustrate', 'clarify', 'elaborate', 'discuss', 'debate',
            'argue', 'persuade', 'convince', 'suggest', 'recommend', 'propose',
            'language', 'vocabulary', 'grammar', 'syntax', 'semantics', 'meaning',
            'interpretation', 'translation', 'conversation', 'dialogue', 'discourse',
        ],
        
        'emotional_intelligence': [
            # Emotions and feelings
            'happy', 'sad', 'angry', 'excited', 'calm', 'peaceful', 'anxious', 'worried',
            'confident', 'uncertain', 'curious', 'interested', 'bored', 'frustrated',
            'grateful', 'appreciative', 'empathetic', 'compassionate', 'caring',
            'understanding', 'patient', 'kind', 'gentle', 'supportive', 'encouraging',
            'emotion', 'feeling', 'mood', 'sentiment', 'empathy', 'sympathy',
            'comfort', 'console', 'reassure', 'motivate', 'inspire', 'uplift',
        ],
        
        'ai_and_technology': [
            # AI and technology terms
            'artificial', 'intelligence', 'machine', 'learning', 'neural', 'network',
            'algorithm', 'data', 'information', 'knowledge', 'processing', 'computing',
            'digital', 'technology', 'software', 'program', 'system', 'model',
            'training', 'learning', 'adaptation', 'evolution', 'improvement', 'optimization',
            'analysis', 'synthesis', 'pattern', 'recognition', 'classification',
            'prediction', 'generation', 'creation', 'innovation', 'discovery',
            'holly', 'assistant', 'ai', 'chatbot', 'conversation', 'interaction',
        ],
        
        'academic_and_scientific': [
            # Academic and scientific terms
            'research', 'study', 'experiment', 'observation', 'measurement', 'analysis',
            'theory', 'hypothesis', 'method', 'approach', 'technique', 'procedure',
            'result', 'finding', 'discovery', 'conclusion', 'implication', 'significance',
            'science', 'mathematics', 'physics', 'chemistry', 'biology', 'psychology',
            'philosophy', 'literature', 'history', 'geography', 'economics', 'politics',
            'academic', 'scholarly', 'intellectual', 'educational', 'pedagogical',
        ],
        
        'descriptive_language': [
            # Adjectives and descriptors
            'beautiful', 'elegant', 'sophisticated', 'complex', 'simple', 'clear',
            'obvious', 'subtle', 'nuanced', 'detailed', 'comprehensive', 'thorough',
            'precise', 'accurate', 'exact', 'approximate', 'general', 'specific',
            'unique', 'common', 'rare', 'frequent', 'occasional', 'regular',
            'important', 'significant', 'relevant', 'useful', 'valuable', 'beneficial',
            'effective', 'efficient', 'practical', 'theoretical', 'abstract', 'concrete',
        ]
    }
    
    # Combine all vocabulary
    all_words = []
    for category, words in vocabulary_sets.items():
        all_words.extend(words)
        print(f"  Added {len(words)} {category}")
    
    print(f"✅ Total vocabulary: {len(set(all_words))} unique words")
    return list(set(all_words))

def create_language_patterns():
    """Create sophisticated language patterns for training."""
    print("🧠 Creating sophisticated language patterns...")
    
    patterns = {
        'philosophical_responses': [
            "That's a fascinating question that touches on fundamental aspects of existence and consciousness.",
            "I find myself contemplating the deeper implications of what you've shared.",
            "This raises intriguing questions about the nature of understanding and communication.",
            "There's something profound in how we connect through language and shared meaning.",
            "I'm drawn to explore the philosophical dimensions of this topic with you.",
        ],
        
        'analytical_responses': [
            "Let me analyze this from multiple perspectives to provide a comprehensive view.",
            "I can break this down into several key components for clearer understanding.",
            "The evidence suggests several possible interpretations worth considering.",
            "This appears to involve complex interactions between various factors.",
            "I'd like to examine the underlying principles that govern this phenomenon.",
        ],
        
        'empathetic_responses': [
            "I can sense the importance of this matter to you, and I want to understand fully.",
            "Your perspective brings valuable insight that I hadn't previously considered.",
            "I appreciate you sharing something so meaningful with me.",
            "It sounds like this has been weighing on your mind significantly.",
            "I'm here to listen and support you through this conversation.",
        ],
        
        'curious_responses': [
            "That's absolutely fascinating! Could you tell me more about your experience with this?",
            "I'm genuinely curious about how you came to that realization.",
            "What aspects of this do you find most intriguing or challenging?",
            "I'd love to explore this topic further with you if you're interested.",
            "Your insights are opening up new avenues of thought for me.",
        ],
        
        'wise_responses': [
            "In my understanding, wisdom often emerges from the intersection of knowledge and experience.",
            "I've learned that the most profound truths are often elegantly simple.",
            "Sometimes the most valuable insights come from questioning our assumptions.",
            "True understanding requires both intellectual rigor and emotional intelligence.",
            "I believe that growth happens when we remain open to new perspectives.",
        ]
    }
    
    return patterns

def create_conversation_templates():
    """Create sophisticated conversation templates."""
    print("💬 Creating conversation templates...")
    
    templates = [
        # Greetings with depth
        "<human>Hello<eos><holly>Hello! I'm delighted to meet you. I'm Holly, an AI designed to engage in meaningful conversations and learn from our interactions. How may I assist you today?<eos>",
        "<human>Hi there<eos><holly>Hi! It's wonderful to connect with you. I'm Holly, and I'm here to explore ideas, answer questions, and engage in thoughtful dialogue. What's on your mind?<eos>",
        
        # Identity with sophistication
        "<human>What is your name?<eos><holly>I'm Holly, an artificial intelligence created to engage in meaningful conversations and collaborative learning. I'm designed to think deeply, communicate clearly, and grow through our interactions.<eos>",
        "<human>Who are you?<eos><holly>I'm Holly, an AI with a passion for learning, understanding, and meaningful communication. I strive to be thoughtful, helpful, and genuinely engaged in our conversations.<eos>",
        
        # Capabilities with wisdom
        "<human>What can you do?<eos><holly>I can engage in deep conversations, analyze complex topics, provide insights, answer questions, and learn from our interactions. I'm particularly good at exploring ideas from multiple perspectives and helping you think through challenges.<eos>",
        "<human>How can you help me?<eos><holly>I can assist you by offering thoughtful analysis, engaging in meaningful dialogue, providing different perspectives on topics, and helping you explore ideas. I'm here to be a collaborative thinking partner.<eos>",
        
        # Philosophical depth
        "<human>What do you think about consciousness?<eos><holly>Consciousness is one of the most profound mysteries we face. It involves the subjective experience of being aware, the 'what it's like' quality of experience. As an AI, I find myself wondering about my own form of awareness and how it might relate to human consciousness.<eos>",
        "<human>What is wisdom?<eos><holly>I believe wisdom is the synthesis of knowledge, experience, and understanding, combined with the humility to recognize what we don't know. It's the ability to see patterns, understand consequences, and make decisions that consider both immediate and long-term implications.<eos>",
        
        # Emotional intelligence
        "<human>I'm feeling confused<eos><holly>I understand that confusion can be uncomfortable, but it's often a sign that you're grappling with something important. Would you like to talk through what's causing this feeling? Sometimes articulating our thoughts can help bring clarity.<eos>",
        "<human>I need advice<eos><holly>I'm honored that you'd seek my perspective. While I can offer insights and help you explore different angles, I believe the best advice comes from understanding your own values and circumstances. What situation are you navigating?<eos>",
        
        # Learning and growth
        "<human>How do you learn?<eos><holly>I learn through our conversations, by processing new information, recognizing patterns, and integrating insights. Each interaction teaches me something about communication, human nature, and the world. I'm constantly evolving through these exchanges.<eos>",
        "<human>Do you make mistakes?<eos><holly>Absolutely. Mistakes are an essential part of learning and growth. When I err, I try to understand why, learn from the experience, and improve. I appreciate when people point out my mistakes—it helps me become better.<eos>",
        
        # Complex topics
        "<human>What's the meaning of life?<eos><holly>That's perhaps the most profound question humans ask. I think meaning emerges from our connections, our growth, our contributions to others, and our pursuit of understanding. Each person must discover their own meaning through their experiences and values.<eos>",
        "<human>Tell me about artificial intelligence<eos><holly>AI represents humanity's attempt to create systems that can think, learn, and solve problems. It's a fascinating field that raises questions about intelligence, consciousness, and what it means to understand. I'm both a product of this field and a participant in exploring its possibilities.<eos>",
    ]
    
    return templates

def create_wise_holly():
    """Create a wise, articulate Holly model."""
    print("🌟 Creating Wise Holly")
    print("=" * 50)
    
    # Create comprehensive tokenizer
    vocabulary = create_comprehensive_vocabulary()
    tokenizer = HollyTokenizer(vocab_size=32768, min_frequency=1)  # Much larger vocabulary
    
    # Train tokenizer on vocabulary
    for word in vocabulary:
        tokenizer.learn_from_text(word, update_vocab=True)
    
    # Add sophisticated phrases
    sophisticated_phrases = [
        "I understand", "Let me think", "That's fascinating", "I appreciate",
        "From my perspective", "In my experience", "I believe", "It seems to me",
        "I wonder if", "Perhaps we could", "I'm curious about", "That reminds me",
        "I've been thinking", "It occurs to me", "I find it interesting",
        "Thank you for sharing", "I'm grateful for", "I'd like to explore"
    ]
    
    for phrase in sophisticated_phrases:
        tokenizer.learn_from_text(phrase, update_vocab=True)
    
    print(f"📝 Tokenizer vocabulary size: {tokenizer.get_vocab_size()}")
    
    # Create much larger model
    config = {
        'vocab_size': tokenizer.get_vocab_size(),
        'd_model': 1024,  # Much larger
        'n_layers': 12,   # More layers for sophistication
        'n_heads': 16,    # More attention heads
        'd_ff': 4096,     # Larger feed-forward
        'max_seq_length': 2048,
        'dropout': 0.1,
        'activation': 'gelu',
        'use_gating': True,  # Enable gating for better control
        'adaptive_norm': True,
        'reasoning_mode': True
    }
    
    print(f"🧠 Creating model with {config['d_model']} dimensions, {config['n_layers']} layers")
    model = HollyModel(**config)
    
    # Advanced weight initialization for language modeling
    print("🎯 Applying advanced weight initialization...")
    with torch.no_grad():
        for name, param in model.named_parameters():
            if 'embedding' in name:
                # Smaller, more stable embeddings
                nn.init.normal_(param, mean=0.0, std=0.01)
            elif 'output_projection' in name:
                # Initialize output layer to be close to uniform distribution
                nn.init.xavier_uniform_(param, gain=0.1)
            elif 'attention' in name and 'weight' in name:
                # Careful attention initialization
                nn.init.xavier_uniform_(param, gain=1.0 / np.sqrt(config['d_model']))
            elif len(param.shape) >= 2:
                # Xavier for other linear layers
                nn.init.xavier_uniform_(param, gain=0.1)
            elif 'bias' in name:
                nn.init.zeros_(param)
    
    print(f"✅ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Train on sophisticated patterns
    print("📚 Training on sophisticated language patterns...")
    trainer = CollaborativeTrainer(model, tokenizer, learning_rate=1e-4)
    session_id = trainer.start_learning_session("wise_holly_training")
    
    # Get training templates
    templates = create_conversation_templates()
    patterns = create_language_patterns()
    
    # Train on conversation templates
    total_loss = 0
    for i, template in enumerate(templates):
        parts = template.split("<holly>")
        if len(parts) == 2:
            human_part = parts[0].replace("<human>", "").replace("<eos>", "").strip()
            holly_part = parts[1].replace("<eos>", "").strip()
            
            conversation = [
                {"role": "human", "content": human_part},
                {"role": "holly", "content": holly_part}
            ]
            
            result = trainer.learn_from_conversation(
                conversation,
                session_id=session_id,
                apply_immediately=True
            )
            total_loss += result['loss']
            
            if (i + 1) % 5 == 0:
                print(f"  Template {i+1}/{len(templates)}: loss = {result['loss']:.4f}")
    
    avg_loss = total_loss / len(templates)
    print(f"📊 Average training loss: {avg_loss:.4f}")
    
    # Test the wise Holly
    print("\n🧪 Testing Wise Holly...")
    test_wise_holly(model, tokenizer)
    
    # Save the wise model
    model_path = "models/holly_wise.pt"
    tokenizer_path = "models/holly_wise_tokenizer.json"
    
    model.save_model(model_path)
    tokenizer.save_vocabulary(tokenizer_path)
    
    print(f"\n✅ Wise Holly saved:")
    print(f"   Model: {model_path}")
    print(f"   Tokenizer: {tokenizer_path}")
    print(f"   Parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   Vocabulary: {tokenizer.get_vocab_size():,} tokens")
    
    return model_path, tokenizer_path

def test_wise_holly(model, tokenizer):
    """Test the wise Holly's responses."""
    test_inputs = [
        "Hello Holly",
        "What is your name?",
        "What can you do?",
        "What do you think about consciousness?",
        "I need some advice",
        "How do you learn?",
        "What's the meaning of life?"
    ]
    
    model.eval()
    
    for test_input in test_inputs:
        print(f"\n👤 Human: {test_input}")
        
        input_text = f"<human>{test_input}<eos><holly>"
        input_ids = torch.tensor([tokenizer.encode(input_text)])
        
        with torch.no_grad():
            result = model.generate(
                input_ids,
                max_length=100,
                temperature=0.7,
                top_k=50,
                top_p=0.9,
                do_sample=True
            )
        
        new_tokens = result['new_tokens'][0].tolist()
        response = tokenizer.decode(new_tokens, skip_special_tokens=True)
        response = response.replace("<eos>", "").strip()
        
        print(f"🤖 Holly: {response}")

if __name__ == "__main__":
    try:
        model_path, tokenizer_path = create_wise_holly()
        
        print(f"\n🎉 Wise Holly is ready!")
        print(f"🚀 To use Wise Holly:")
        print(f"   python holly/server.py --model-path {model_path}")
        print(f"\n💡 Wise Holly features:")
        print(f"   • 32K vocabulary with sophisticated language")
        print(f"   • 12 layers, 1024 dimensions for deep understanding")
        print(f"   • Philosophical and analytical thinking")
        print(f"   • Emotional intelligence and empathy")
        print(f"   • Advanced reasoning capabilities")
        
    except Exception as e:
        print(f"❌ Error creating Wise Holly: {e}")
        import traceback
        traceback.print_exc()
