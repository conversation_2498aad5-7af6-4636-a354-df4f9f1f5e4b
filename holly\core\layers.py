"""
Core neural network layers for <PERSON>'s transformer architecture.

This module implements the building blocks including feed-forward networks,
transformer blocks, and layer normalization with adaptive features.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, Tuple
from holly.core.attention import TransparentAttention


class AdaptiveLayerNorm(nn.Module):
    """
    Layer normalization with adaptive scaling.
    
    This allows <PERSON> to adjust normalization parameters
    as she learns and evolves.
    """
    
    def __init__(self, d_model: int, eps: float = 1e-6, adaptive: bool = True):
        super().__init__()
        self.d_model = d_model
        self.eps = eps
        self.adaptive = adaptive
        
        # Standard layer norm parameters
        self.gamma = nn.Parameter(torch.ones(d_model))
        self.beta = nn.Parameter(torch.zeros(d_model))
        
        # Adaptive scaling factor
        if adaptive:
            self.adaptive_scale = nn.Parameter(torch.ones(1))
        else:
            self.register_buffer('adaptive_scale', torch.ones(1))
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Apply adaptive layer normalization."""
        mean = x.mean(dim=-1, keepdim=True)
        std = x.std(dim=-1, keepdim=True)
        
        normalized = (x - mean) / (std + self.eps)
        output = self.gamma * normalized + self.beta
        
        if self.adaptive:
            output = output * self.adaptive_scale
        
        return output


class FeedForward(nn.Module):
    """
    Feed-forward network with adaptive capacity.
    
    Features:
    - Configurable expansion ratio
    - Optional gating mechanism
    - Adaptive dropout
    """
    
    def __init__(
        self, 
        d_model: int, 
        d_ff: Optional[int] = None, 
        dropout: float = 0.1,
        activation: str = "gelu",
        use_gating: bool = False
    ):
        super().__init__()
        self.d_model = d_model
        self.d_ff = d_ff or 4 * d_model
        self.use_gating = use_gating
        
        # Main feed-forward layers
        self.linear1 = nn.Linear(d_model, self.d_ff)
        self.linear2 = nn.Linear(self.d_ff, d_model)
        
        # Optional gating mechanism
        if use_gating:
            self.gate = nn.Linear(d_model, self.d_ff)
        
        # Activation function
        if activation == "gelu":
            self.activation = nn.GELU()
        elif activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "swish":
            self.activation = nn.SiLU()
        else:
            raise ValueError(f"Unknown activation: {activation}")
        
        self.dropout = nn.Dropout(dropout)
        
        # For tracking activation patterns
        self.activation_stats = {
            'mean_activation': 0.0,
            'activation_sparsity': 0.0,
            'forward_count': 0
        }
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through feed-forward network."""
        # First linear transformation
        hidden = self.linear1(x)
        
        # Apply gating if enabled
        if self.use_gating:
            gate_values = torch.sigmoid(self.gate(x))
            hidden = hidden * gate_values
        
        # Activation
        hidden = self.activation(hidden)
        
        # Update activation statistics
        self._update_activation_stats(hidden)
        
        # Dropout and second linear transformation
        hidden = self.dropout(hidden)
        output = self.linear2(hidden)
        
        return output
    
    def _update_activation_stats(self, activations: torch.Tensor):
        """Update statistics about activation patterns."""
        with torch.no_grad():
            self.activation_stats['forward_count'] += 1
            
            # Running average of mean activation
            current_mean = activations.mean().item()
            alpha = 0.01  # Smoothing factor
            self.activation_stats['mean_activation'] = (
                alpha * current_mean + 
                (1 - alpha) * self.activation_stats['mean_activation']
            )
            
            # Sparsity (fraction of near-zero activations)
            sparsity = (activations.abs() < 0.01).float().mean().item()
            self.activation_stats['activation_sparsity'] = (
                alpha * sparsity + 
                (1 - alpha) * self.activation_stats['activation_sparsity']
            )
    
    def get_stats(self) -> Dict[str, float]:
        """Get activation statistics."""
        return self.activation_stats.copy()


class TransformerBlock(nn.Module):
    """
    Complete transformer block with attention and feed-forward.
    
    Features:
    - Transparent attention mechanism
    - Adaptive layer normalization
    - Residual connections with optional scaling
    - Built-in monitoring and statistics
    """
    
    def __init__(
        self,
        d_model: int,
        n_heads: int = 8,
        d_ff: Optional[int] = None,
        dropout: float = 0.1,
        activation: str = "gelu",
        use_gating: bool = False,
        adaptive_norm: bool = True
    ):
        super().__init__()
        self.d_model = d_model
        
        # Attention mechanism
        self.attention = TransparentAttention(d_model, n_heads, dropout)
        
        # Feed-forward network
        self.feed_forward = FeedForward(
            d_model, d_ff, dropout, activation, use_gating
        )
        
        # Layer normalization
        self.norm1 = AdaptiveLayerNorm(d_model, adaptive=adaptive_norm)
        self.norm2 = AdaptiveLayerNorm(d_model, adaptive=adaptive_norm)
        
        # Residual scaling (learnable)
        self.residual_scale1 = nn.Parameter(torch.ones(1))
        self.residual_scale2 = nn.Parameter(torch.ones(1))
        
        # Block-level statistics
        self.block_stats = {
            'attention_entropy': 0.0,
            'residual_magnitude': 0.0,
            'forward_count': 0
        }
    
    def forward(
        self, 
        x: torch.Tensor, 
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Forward pass through transformer block.
        
        Args:
            x: Input tensor [batch_size, seq_len, d_model]
            mask: Optional attention mask
            return_attention: Whether to return attention weights
            
        Returns:
            output: Transformed tensor
            attention_weights: Optional attention weights for transparency
        """
        # Self-attention with residual connection
        attn_output, attention_weights = self.attention(
            x, x, x, mask, return_attention=True
        )
        x1 = self.norm1(x + self.residual_scale1 * attn_output)
        
        # Feed-forward with residual connection
        ff_output = self.feed_forward(x1)
        x2 = self.norm2(x1 + self.residual_scale2 * ff_output)
        
        # Update block statistics
        self._update_block_stats(attn_output, ff_output)
        
        if return_attention:
            return x2, attention_weights
        else:
            return x2, None
    
    def _update_block_stats(self, attn_output: torch.Tensor, ff_output: torch.Tensor):
        """Update block-level statistics."""
        with torch.no_grad():
            self.block_stats['forward_count'] += 1
            alpha = 0.01  # Smoothing factor
            
            # Attention entropy (if available)
            if hasattr(self.attention, 'attention_entropy') and self.attention.attention_entropy is not None:
                current_entropy = self.attention.attention_entropy.item()
                self.block_stats['attention_entropy'] = (
                    alpha * current_entropy + 
                    (1 - alpha) * self.block_stats['attention_entropy']
                )
            
            # Residual magnitude
            residual_mag = (attn_output.norm() + ff_output.norm()).item()
            self.block_stats['residual_magnitude'] = (
                alpha * residual_mag + 
                (1 - alpha) * self.block_stats['residual_magnitude']
            )
    
    def get_transparency_info(self) -> Dict[str, Any]:
        """Get transparency information about this block."""
        info = {
            "block_stats": self.block_stats.copy(),
            "attention_summary": self.attention.get_attention_summary(),
            "ff_stats": self.feed_forward.get_stats(),
            "residual_scales": {
                "attention": float(self.residual_scale1),
                "feed_forward": float(self.residual_scale2)
            }
        }
        
        return info
