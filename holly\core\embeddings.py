"""
Embedding layers for <PERSON> with adaptive and learnable components.

This module implements token and positional embeddings that can
evolve and adapt as <PERSON> learns from conversations.
"""

import torch
import torch.nn as nn
import math
from typing import Optional, Dict, Any


class HollyEmbeddings(nn.Module):
    """
    Adaptive embedding layer for <PERSON>.
    
    Features:
    - Learnable token embeddings that can expand vocabulary
    - Sinusoidal + learnable positional encodings
    - Adaptive scaling based on training progress
    """
    
    def __init__(
        self, 
        vocab_size: int, 
        d_model: int, 
        max_seq_length: int = 2048,
        dropout: float = 0.1,
        adaptive_scaling: bool = True
    ):
        super().__init__()
        self.d_model = d_model
        self.vocab_size = vocab_size
        self.max_seq_length = max_seq_length
        self.adaptive_scaling = adaptive_scaling
        
        # Token embeddings
        self.token_embeddings = nn.Embedding(vocab_size, d_model)
        
        # Hybrid positional encoding: sinusoidal + learnable
        self.register_buffer('sinusoidal_pos', self._create_sinusoidal_encoding())
        self.learnable_pos = nn.Parameter(torch.zeros(max_seq_length, d_model))
        
        # Embedding scaling factor (can be learned)
        if adaptive_scaling:
            self.scale_factor = nn.Parameter(torch.tensor(math.sqrt(d_model)))
        else:
            self.register_buffer('scale_factor', torch.tensor(math.sqrt(d_model)))
        
        self.dropout = nn.Dropout(dropout)
        
        # For tracking embedding evolution
        self.embedding_stats = {
            'token_usage_count': torch.zeros(vocab_size),
            'position_usage_count': torch.zeros(max_seq_length)
        }
        
        self._initialize_embeddings()
    
    def _create_sinusoidal_encoding(self) -> torch.Tensor:
        """Create sinusoidal positional encodings."""
        pe = torch.zeros(self.max_seq_length, self.d_model)
        position = torch.arange(0, self.max_seq_length).unsqueeze(1).float()
        
        div_term = torch.exp(
            torch.arange(0, self.d_model, 2).float() * 
            -(math.log(10000.0) / self.d_model)
        )
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return pe
    
    def _initialize_embeddings(self):
        """Initialize embeddings with careful scaling."""
        # Initialize token embeddings with Xavier uniform
        nn.init.xavier_uniform_(self.token_embeddings.weight)
        
        # Initialize learnable positional embeddings with small values
        nn.init.normal_(self.learnable_pos, mean=0, std=0.02)
    
    def forward(self, input_ids: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through embeddings.
        
        Args:
            input_ids: Token IDs [batch_size, seq_len]
            
        Returns:
            embeddings: Combined token + positional embeddings [batch_size, seq_len, d_model]
        """
        batch_size, seq_len = input_ids.size()
        
        # Update usage statistics (for analysis)
        self._update_usage_stats(input_ids)
        
        # Token embeddings
        token_emb = self.token_embeddings(input_ids)
        
        # Positional embeddings (hybrid approach)
        positions = torch.arange(seq_len, device=input_ids.device)
        sinusoidal_pos = self.sinusoidal_pos[:seq_len]
        learnable_pos = self.learnable_pos[:seq_len]
        
        # Combine sinusoidal and learnable positional encodings
        pos_emb = sinusoidal_pos + learnable_pos
        pos_emb = pos_emb.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Combine and scale
        embeddings = (token_emb + pos_emb) * self.scale_factor
        
        return self.dropout(embeddings)
    
    def _update_usage_stats(self, input_ids: torch.Tensor):
        """Update statistics about token and position usage."""
        with torch.no_grad():
            # Count token usage
            unique_tokens, counts = torch.unique(input_ids, return_counts=True)
            for token, count in zip(unique_tokens, counts):
                if token < self.vocab_size:
                    self.embedding_stats['token_usage_count'][token] += count
            
            # Count position usage
            seq_len = input_ids.size(1)
            for pos in range(min(seq_len, self.max_seq_length)):
                self.embedding_stats['position_usage_count'][pos] += input_ids.size(0)
    
    def expand_vocabulary(self, new_vocab_size: int):
        """
        Dynamically expand vocabulary size.
        
        This allows Holly to learn new tokens during conversations.
        """
        if new_vocab_size <= self.vocab_size:
            return
        
        # Create new embedding layer
        old_embeddings = self.token_embeddings.weight.data
        new_embeddings = nn.Embedding(new_vocab_size, self.d_model)
        
        # Copy old embeddings and initialize new ones
        with torch.no_grad():
            new_embeddings.weight[:self.vocab_size] = old_embeddings
            nn.init.xavier_uniform_(new_embeddings.weight[self.vocab_size:])
        
        self.token_embeddings = new_embeddings
        self.vocab_size = new_vocab_size
        
        # Expand usage statistics
        new_stats = torch.zeros(new_vocab_size)
        new_stats[:len(self.embedding_stats['token_usage_count'])] = self.embedding_stats['token_usage_count']
        self.embedding_stats['token_usage_count'] = new_stats
    
    def get_embedding_insights(self) -> Dict[str, Any]:
        """Get insights about embedding usage and evolution."""
        token_usage = self.embedding_stats['token_usage_count']
        pos_usage = self.embedding_stats['position_usage_count']
        
        return {
            "vocab_size": self.vocab_size,
            "d_model": self.d_model,
            "most_used_tokens": torch.topk(token_usage, k=10).indices.tolist(),
            "least_used_tokens": torch.topk(token_usage, k=10, largest=False).indices.tolist(),
            "avg_sequence_length": float((pos_usage > 0).sum()),
            "token_usage_entropy": float(self._calculate_entropy(token_usage)),
            "embedding_norm_stats": {
                "mean": float(self.token_embeddings.weight.norm(dim=1).mean()),
                "std": float(self.token_embeddings.weight.norm(dim=1).std()),
                "max": float(self.token_embeddings.weight.norm(dim=1).max())
            },
            "scale_factor": float(self.scale_factor) if hasattr(self.scale_factor, 'item') else float(self.scale_factor)
        }
    
    def _calculate_entropy(self, distribution: torch.Tensor) -> torch.Tensor:
        """Calculate entropy of a distribution."""
        # Normalize to probabilities
        probs = distribution / (distribution.sum() + 1e-8)
        # Add small epsilon to avoid log(0)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8))
        return entropy
