"""
FastAPI server for <PERSON>'s web interface.

This module implements the backend API server that handles
chat requests, model interactions, and real-time communication.
"""

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import json
import asyncio
import logging
from datetime import datetime
import torch

from holly.core.model import HollyModel
from holly.core.tokenizer import Holly<PERSON>oken<PERSON>
from holly.learning.trainer import CollaborativeTrainer


# Pydantic models for API
class ChatMessage(BaseModel):
    role: str
    content: str
    timestamp: Optional[str] = None

class ChatRequest(BaseModel):
    message: str
    conversation_history: List[ChatMessage] = []
    show_reasoning: bool = False
    temperature: float = 0.8
    max_length: int = 150

class FeedbackRequest(BaseModel):
    original_response: str
    corrected_response: str
    feedback_type: str = "correction"

class ModelInsightsRequest(BaseModel):
    include_detailed_stats: bool = False


class HollyServer:
    """
    Main server class for <PERSON>'s web interface.
    """
    
    def __init__(
        self,
        model_path: Optional[str] = None,
        host: str = "localhost",
        port: int = 8000,
        debug: bool = True
    ):
        self.host = host
        self.port = port
        self.debug = debug
        
        # Initialize FastAPI app
        self.app = FastAPI(
            title="Holly - Collaborative AI System",
            description="Interactive interface for Holly AI development",
            version="0.1.0"
        )
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("holly.server")
        
        # Initialize Holly components
        self.tokenizer = HollyTokenizer()
        
        if model_path and torch.cuda.is_available():
            device = "cuda"
        else:
            device = "cpu"
            
        if model_path:
            self.model = HollyModel.load_model(model_path, device=device)
            self.logger.info(f"Loaded Holly model from {model_path}")
        else:
            self.model = HollyModel(
                vocab_size=self.tokenizer.get_vocab_size(),
                d_model=256,  # Smaller for initial development
                n_layers=4,
                n_heads=8
            )
            self.logger.info("Created new Holly model")
        
        # Initialize trainer
        self.trainer = CollaborativeTrainer(self.model, self.tokenizer)
        
        # Active WebSocket connections
        self.active_connections: List[WebSocket] = []
        
        # Current learning session
        self.current_session = None
        
        # Setup routes
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def get_chat_interface():
            """Serve the main chat interface."""
            return self._get_chat_html()
        
        @self.app.post("/api/chat")
        async def chat_with_holly(request: ChatRequest) -> Dict[str, Any]:
            """Handle chat requests to Holly."""
            try:
                # Prepare input
                conversation_text = self._format_conversation(request.conversation_history + [
                    ChatMessage(role="human", content=request.message)
                ])
                
                input_ids = torch.tensor([self.tokenizer.encode(conversation_text)])
                
                # Generate response
                with torch.no_grad():
                    generation_result = self.model.generate(
                        input_ids,
                        max_length=request.max_length,
                        temperature=request.temperature,
                        show_reasoning=request.show_reasoning
                    )
                
                # Decode response
                new_tokens = generation_result['new_tokens'][0].tolist()
                response_text = self.tokenizer.decode(new_tokens, skip_special_tokens=True)
                
                # Learn from conversation if enabled
                if self.current_session:
                    conversation_data = [
                        {"role": "human", "content": request.message},
                        {"role": "holly", "content": response_text}
                    ]
                    learning_stats = self.trainer.learn_from_conversation(
                        conversation_data,
                        session_id=self.current_session
                    )
                else:
                    learning_stats = None
                
                # Prepare response
                response = {
                    "response": response_text,
                    "timestamp": datetime.now().isoformat(),
                    "generation_stats": {
                        "tokens_generated": generation_result['generation_length'],
                        "temperature": request.temperature
                    }
                }
                
                if request.show_reasoning and 'reasoning_trace' in generation_result:
                    response["reasoning"] = generation_result['reasoning_trace']
                
                if learning_stats:
                    response["learning_stats"] = learning_stats
                
                # Broadcast to WebSocket connections
                await self._broadcast_message({
                    "type": "chat_response",
                    "data": response
                })
                
                return response
                
            except Exception as e:
                self.logger.error(f"Error in chat endpoint: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/feedback")
        async def provide_feedback(request: FeedbackRequest) -> Dict[str, Any]:
            """Handle feedback from users."""
            try:
                feedback_result = self.trainer.process_feedback(
                    request.original_response,
                    request.corrected_response,
                    request.feedback_type,
                    session_id=self.current_session
                )
                
                # Broadcast feedback to WebSocket connections
                await self._broadcast_message({
                    "type": "feedback_received",
                    "data": feedback_result
                })
                
                return feedback_result
                
            except Exception as e:
                self.logger.error(f"Error in feedback endpoint: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/insights")
        async def get_model_insights(include_detailed: bool = False) -> Dict[str, Any]:
            """Get insights about Holly's current state."""
            try:
                insights = self.model.get_model_insights()
                training_summary = self.trainer.get_training_summary()
                
                response = {
                    "model_insights": insights,
                    "training_summary": training_summary,
                    "current_session": self.current_session,
                    "timestamp": datetime.now().isoformat()
                }
                
                return response
                
            except Exception as e:
                self.logger.error(f"Error in insights endpoint: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/session/start")
        async def start_learning_session(session_name: Optional[str] = None) -> Dict[str, str]:
            """Start a new learning session."""
            try:
                session_id = self.trainer.start_learning_session(session_name)
                self.current_session = session_id
                
                await self._broadcast_message({
                    "type": "session_started",
                    "data": {"session_id": session_id}
                })
                
                return {"session_id": session_id, "status": "started"}
                
            except Exception as e:
                self.logger.error(f"Error starting session: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/session/end")
        async def end_learning_session() -> Dict[str, str]:
            """End the current learning session."""
            if self.current_session:
                old_session = self.current_session
                self.current_session = None
                
                await self._broadcast_message({
                    "type": "session_ended",
                    "data": {"session_id": old_session}
                })
                
                return {"session_id": old_session, "status": "ended"}
            else:
                return {"status": "no_active_session"}
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time communication."""
            await self._handle_websocket(websocket)
    
    async def _handle_websocket(self, websocket: WebSocket):
        """Handle WebSocket connections."""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        try:
            while True:
                # Keep connection alive and handle incoming messages
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                
        except WebSocketDisconnect:
            self.active_connections.remove(websocket)
    
    async def _broadcast_message(self, message: Dict[str, Any]):
        """Broadcast message to all connected WebSocket clients."""
        if self.active_connections:
            message_text = json.dumps(message)
            for connection in self.active_connections.copy():
                try:
                    await connection.send_text(message_text)
                except:
                    # Remove disconnected connections
                    self.active_connections.remove(connection)
    
    def _format_conversation(self, messages: List[ChatMessage]) -> str:
        """Format conversation history for model input."""
        conversation_text = ""
        for message in messages:
            if message.role == "human":
                conversation_text += f"<human>{message.content}<eos>"
            elif message.role == "holly":
                conversation_text += f"<holly>{message.content}<eos>"
        return conversation_text
    
    def _get_chat_html(self) -> str:
        """Return the HTML for the chat interface."""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Holly - Collaborative AI</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { text-align: center; margin-bottom: 30px; }
                .chat-container { height: 400px; border: 1px solid #ddd; border-radius: 5px; padding: 15px; overflow-y: auto; margin-bottom: 20px; background: #fafafa; }
                .message { margin-bottom: 15px; padding: 10px; border-radius: 5px; }
                .human { background: #e3f2fd; margin-left: 20px; }
                .holly { background: #f3e5f5; margin-right: 20px; }
                .input-container { display: flex; gap: 10px; }
                .message-input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
                .send-button { padding: 10px 20px; background: #2196f3; color: white; border: none; border-radius: 5px; cursor: pointer; }
                .send-button:hover { background: #1976d2; }
                .controls { margin-bottom: 20px; display: flex; gap: 10px; align-items: center; }
                .status { padding: 5px 10px; background: #e8f5e8; border-radius: 3px; font-size: 12px; }
                .reasoning { background: #fff3e0; padding: 10px; margin-top: 10px; border-radius: 5px; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🌟 Holly - Collaborative AI System</h1>
                    <p>An experimental AI that learns and grows through our conversations</p>
                </div>
                
                <div class="controls">
                    <button onclick="startSession()">Start Learning Session</button>
                    <button onclick="endSession()">End Session</button>
                    <label><input type="checkbox" id="showReasoning"> Show Reasoning</label>
                    <div class="status" id="status">Ready</div>
                </div>
                
                <div class="chat-container" id="chatContainer"></div>
                
                <div class="input-container">
                    <input type="text" id="messageInput" class="message-input" placeholder="Type your message to Holly..." onkeypress="handleKeyPress(event)">
                    <button class="send-button" onclick="sendMessage()">Send</button>
                </div>
            </div>
            
            <script>
                let ws = null;
                let currentSession = null;
                
                // Initialize WebSocket connection
                function initWebSocket() {
                    ws = new WebSocket('ws://localhost:8000/ws');
                    ws.onmessage = function(event) {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    };
                }
                
                function handleWebSocketMessage(message) {
                    if (message.type === 'session_started') {
                        currentSession = message.data.session_id;
                        updateStatus('Learning session active: ' + currentSession);
                    } else if (message.type === 'session_ended') {
                        currentSession = null;
                        updateStatus('Ready');
                    }
                }
                
                function updateStatus(text) {
                    document.getElementById('status').textContent = text;
                }
                
                function handleKeyPress(event) {
                    if (event.key === 'Enter') {
                        sendMessage();
                    }
                }
                
                async function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const message = input.value.trim();
                    if (!message) return;
                    
                    // Add human message to chat
                    addMessage('human', message);
                    input.value = '';
                    
                    // Show typing indicator
                    const typingDiv = addMessage('holly', 'Thinking...');
                    
                    try {
                        const response = await fetch('/api/chat', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                message: message,
                                show_reasoning: document.getElementById('showReasoning').checked,
                                temperature: 0.8,
                                max_length: 150
                            })
                        });
                        
                        const data = await response.json();
                        
                        // Remove typing indicator and add actual response
                        typingDiv.remove();
                        const responseDiv = addMessage('holly', data.response);
                        
                        // Add reasoning if available
                        if (data.reasoning) {
                            const reasoningDiv = document.createElement('div');
                            reasoningDiv.className = 'reasoning';
                            reasoningDiv.innerHTML = '<strong>Reasoning:</strong> ' + JSON.stringify(data.reasoning, null, 2);
                            responseDiv.appendChild(reasoningDiv);
                        }
                        
                    } catch (error) {
                        typingDiv.textContent = 'Error: ' + error.message;
                    }
                }
                
                function addMessage(role, content) {
                    const chatContainer = document.getElementById('chatContainer');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message ' + role;
                    messageDiv.innerHTML = '<strong>' + (role === 'human' ? 'You' : 'Holly') + ':</strong> ' + content;
                    chatContainer.appendChild(messageDiv);
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                    return messageDiv;
                }
                
                async function startSession() {
                    try {
                        const response = await fetch('/api/session/start', { method: 'POST' });
                        const data = await response.json();
                        updateStatus('Learning session started: ' + data.session_id);
                    } catch (error) {
                        updateStatus('Error starting session');
                    }
                }
                
                async function endSession() {
                    try {
                        const response = await fetch('/api/session/end', { method: 'POST' });
                        updateStatus('Ready');
                    } catch (error) {
                        updateStatus('Error ending session');
                    }
                }
                
                // Initialize on page load
                window.onload = function() {
                    initWebSocket();
                    addMessage('holly', 'Hello! I\\'m Holly, an experimental AI designed to learn and grow through our conversations. How can we explore together today?');
                };
            </script>
        </body>
        </html>
        """
    
    def run(self):
        """Run the Holly server."""
        import uvicorn
        self.logger.info(f"Starting Holly server on {self.host}:{self.port}")
        uvicorn.run(self.app, host=self.host, port=self.port, log_level="info")
