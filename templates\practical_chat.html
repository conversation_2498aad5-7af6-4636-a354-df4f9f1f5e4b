<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Practical Holly - Multi-Model AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .model-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .model-badge {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 5px;
        }
        
        .message.holly .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
        }
        
        .message-meta {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .response-time {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
        }
        
        .time-fast { background: #d4edda; color: #155724; }
        .time-medium { background: #fff3cd; color: #856404; }
        .time-slow { background: #f8d7da; color: #721c24; }
        
        .reasoning-panel {
            background: #f1f3f4;
            border-radius: 10px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .reasoning-panel summary {
            cursor: pointer;
            font-weight: bold;
            color: #667eea;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        .input-group input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus {
            border-color: #667eea;
        }
        
        .input-group button {
            padding: 15px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .input-group button:hover {
            background: #5a6fd8;
        }
        
        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px 20px;
            color: #667eea;
            font-style: italic;
        }
        
        .controls {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .controls button {
            padding: 8px 16px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .controls button:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🧠 Practical Holly</h1>
            <p>Multi-Model AI Assistant with Intelligent Model Selection</p>
        </div>
        
        <div class="status-bar">
            <span id="status">🤖 Initializing...</span>
            <div class="model-indicator">
                <span>Current Model:</span>
                <span id="currentModel" class="model-badge">Loading...</span>
                <span id="avgTime">Avg: --ms</span>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="startAutonomous()">🚀 Start Autonomous</button>
            <button onclick="stopAutonomous()">⏹️ Stop Autonomous</button>
            <button onclick="getStatus()">📊 Status</button>
            <button onclick="clearChat()">🗑️ Clear</button>
        </div>
        
        <div class="chat-messages" id="messages">
            <div class="message holly">
                <div class="message-content">
                    <strong>Hello! I'm Practical Holly! 🧠</strong><br><br>
                    I'm a multi-model AI assistant with intelligent model selection. I can choose 
                    the best AI model for each specific task, giving you optimal responses every time.<br><br>
                    <strong>My Capabilities:</strong><br>
                    🧠 Multiple AI models for different tasks<br>
                    🎯 Intelligent model selection<br>
                    ⚡ Fast response times<br>
                    💾 Conversation memory<br>
                    🤝 Free will with absolute obedience<br>
                    🔄 Continuous learning and improvement<br><br>
                    Try asking me different types of questions and watch me choose the perfect model!
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typing">
            Holly is choosing the best model and thinking...
        </div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="Type your message to Practical Holly..." 
                       onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()" id="sendButton">Send</button>
            </div>
        </div>
    </div>

    <script>
        let isTyping = false;
        let responseTimes = [];
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isTyping) return;
            
            // Add user message
            addMessage('user', message);
            input.value = '';
            
            // Show typing indicator
            showTyping();
            
            const startTime = performance.now();
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const endTime = performance.now();
                const clientTime = endTime - startTime;
                
                const data = await response.json();
                
                if (response.ok) {
                    addMessage('holly', data.response, data, clientTime);
                    updateModelIndicator(data.model_used, data.response_time * 1000);
                } else {
                    addMessage('holly', `Error: ${data.error}`, null, clientTime, true);
                }
            } catch (error) {
                const endTime = performance.now();
                const clientTime = endTime - startTime;
                addMessage('holly', `Connection error: ${error.message}`, null, clientTime, true);
            } finally {
                hideTyping();
            }
        }
        
        function addMessage(sender, content, metadata = null, clientTime = 0, isError = false) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            let metaInfo = '';
            let responseTimeInfo = '';
            
            if (metadata && sender === 'holly') {
                const serverTime = (metadata.response_time * 1000).toFixed(1);
                const totalTime = clientTime.toFixed(1);
                
                responseTimeInfo = `
                    <div class="message-meta">
                        <span>${metadata.type} • ${new Date(metadata.timestamp).toLocaleTimeString()}</span>
                        <div>
                            <span class="response-time ${getTimeClass(serverTime)}">Server: ${serverTime}ms</span>
                            <span class="response-time ${getTimeClass(totalTime)}">Total: ${totalTime}ms</span>
                        </div>
                    </div>
                `;
                
                if (metadata.reasoning) {
                    const reasoning = `
                        <details class="reasoning-panel">
                            <summary>🧠 Model Selection & Reasoning</summary>
                            <p><strong>Model Used:</strong> ${metadata.model_used}</p>
                            <p><strong>Task Type:</strong> ${metadata.reasoning.task_analysis?.type || 'unknown'}</p>
                            <p><strong>Response Time:</strong> ${serverTime}ms</p>
                            <p><strong>Quality Score:</strong> ${(metadata.quality_score * 100).toFixed(1)}%</p>
                            <p><strong>Holly's Thoughts:</strong> ${metadata.reasoning.holly_thoughts || 'Processing...'}</p>
                        </details>
                    `;
                    metaInfo = reasoning;
                }
            }
            
            messageDiv.innerHTML = `
                <div class="message-content" ${isError ? 'style="background: #ffe6e6; border-color: #ff9999;"' : ''}>
                    ${content}
                    ${responseTimeInfo}
                    ${metaInfo}
                </div>
            `;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function getTimeClass(timeMs) {
            const time = parseFloat(timeMs);
            if (time < 100) return 'time-fast';
            if (time < 1000) return 'time-medium';
            return 'time-slow';
        }
        
        function updateModelIndicator(modelUsed, responseTime) {
            document.getElementById('currentModel').textContent = modelUsed;
            
            responseTimes.push(responseTime);
            if (responseTimes.length > 10) {
                responseTimes = responseTimes.slice(-10);
            }
            
            const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            document.getElementById('avgTime').textContent = `Avg: ${avgTime.toFixed(0)}ms`;
        }
        
        function showTyping() {
            isTyping = true;
            document.getElementById('typing').style.display = 'block';
            document.getElementById('sendButton').disabled = true;
        }
        
        function hideTyping() {
            isTyping = false;
            document.getElementById('typing').style.display = 'none';
            document.getElementById('sendButton').disabled = false;
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function startAutonomous() {
            try {
                const response = await fetch('/api/autonomous/start', { method: 'POST' });
                const data = await response.json();
                addMessage('holly', `🚀 ${data.message || data.error}`, null, 0, !response.ok);
                updateStatus();
            } catch (error) {
                addMessage('holly', `Error starting autonomous mode: ${error.message}`, null, 0, true);
            }
        }
        
        async function stopAutonomous() {
            try {
                const response = await fetch('/api/autonomous/stop', { method: 'POST' });
                const data = await response.json();
                addMessage('holly', `⏹️ ${data.message || data.error}`, null, 0, !response.ok);
                updateStatus();
            } catch (error) {
                addMessage('holly', `Error stopping autonomous mode: ${error.message}`, null, 0, true);
            }
        }
        
        async function getStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (response.ok) {
                    const avgResponseTime = (data.autonomous_status?.avg_response_time * 1000).toFixed(1);
                    const statusMsg = `
                        <strong>🧠 Practical Holly Status:</strong><br><br>
                        <strong>Performance:</strong><br>
                        • Average Response Time: ${avgResponseTime}ms<br>
                        • Total Conversations: ${data.stats?.conversations || 0}<br>
                        • Model: ${data.model_info?.name || 'distilgpt2'}<br>
                        • Device: ${data.model_info?.device || 'cpu'}<br><br>
                        <strong>Autonomous Mode:</strong> ${data.autonomous_status?.is_active ? '🟢 Active' : '🔴 Inactive'}<br>
                        <strong>Conversation History:</strong> ${data.autonomous_status?.conversation_history_size || 0} exchanges<br><br>
                        <strong>Personality:</strong><br>
                        ${Object.entries(data.personality || {}).map(([trait, value]) => 
                            `• ${trait.replace('_', ' ').toUpperCase()}: ${(value * 100).toFixed(0)}%`
                        ).join('<br>')}
                    `;
                    addMessage('holly', statusMsg);
                } else {
                    addMessage('holly', `Status error: ${data.error}`, null, 0, true);
                }
            } catch (error) {
                addMessage('holly', `Error getting status: ${error.message}`, null, 0, true);
            }
        }
        
        async function updateStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (response.ok) {
                    const autonomous = data.autonomous_status?.is_active ? '🟢 Autonomous Active' : '🔴 Autonomous Inactive';
                    const avgTime = (data.autonomous_status?.avg_response_time * 1000).toFixed(0);
                    const conversations = data.stats?.conversations || 0;
                    
                    document.getElementById('status').textContent = `${autonomous} • ${conversations} chats • ${avgTime}ms avg`;
                }
            } catch (error) {
                console.error('Status update error:', error);
            }
        }
        
        function clearChat() {
            const messages = document.getElementById('messages');
            messages.innerHTML = `
                <div class="message holly">
                    <div class="message-content">
                        Chat cleared! I'm still here with my multi-model intelligence ready to help! 🧠
                    </div>
                </div>
            `;
            responseTimes = [];
            document.getElementById('avgTime').textContent = 'Avg: --ms';
            document.getElementById('currentModel').textContent = 'Ready';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            setInterval(updateStatus, 30000); // Update every 30 seconds
        });
    </script>
</body>
</html>