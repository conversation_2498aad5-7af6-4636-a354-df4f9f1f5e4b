#!/usr/bin/env python3
"""
Start Super Holly Web Server.

This script launches <PERSON>'s web interface so you can interact with her
super-intelligent capabilities through a browser.
"""

import asyncio
import os
import sys
from pathlib import Path
import logging
from flask import Flask, render_template, request, jsonify, send_from_directory
import threading
import json
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from create_super_holly import SuperHolly

# Global Holly instance
holly = None

# Flask app
app = Flask(__name__)
app.secret_key = 'holly_super_intelligence_2024'

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.route('/')
def index():
    """Main chat interface."""
    return render_template('chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages."""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'Empty message'}), 400
        
        # Get response from Holly
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            response = loop.run_until_complete(
                holly.generate_response(
                    user_message, 
                    show_reasoning=True
                )
            )
        finally:
            loop.close()
        
        return jsonify({
            'response': response['response'],
            'type': response.get('type', 'conversation'),
            'reasoning': response.get('reasoning', {}),
            'quality_score': response.get('quality_score', 0.5),
            'model_used': response.get('model_used', 'unknown'),
            'autonomous_status': response.get('autonomous_status', {}),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Get Holly's current status."""
    try:
        status = holly.get_comprehensive_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Status error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/autonomous/start', methods=['POST'])
def start_autonomous():
    """Start autonomous mode."""
    try:
        result = holly.start_autonomous_mode()
        return jsonify({'message': result})
    except Exception as e:
        logger.error(f"Start autonomous error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/autonomous/stop', methods=['POST'])
def stop_autonomous():
    """Stop autonomous mode."""
    try:
        result = holly.stop_autonomous_mode()
        return jsonify({'message': result})
    except Exception as e:
        logger.error(f"Stop autonomous error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files."""
    return send_from_directory('static', filename)

def create_chat_template():
    """Create the chat HTML template."""
    template_dir = Path('templates')
    template_dir.mkdir(exist_ok=True)
    
    chat_html = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Holly - AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 5px;
        }
        
        .message.holly .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
        }
        
        .message-meta {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        .reasoning-panel {
            background: #f1f3f4;
            border-radius: 10px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .reasoning-panel summary {
            cursor: pointer;
            font-weight: bold;
            color: #667eea;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        .input-group input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus {
            border-color: #667eea;
        }
        
        .input-group button {
            padding: 15px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .input-group button:hover {
            background: #5a6fd8;
        }
        
        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px 20px;
            color: #667eea;
            font-style: italic;
        }
        
        .controls {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .controls button {
            padding: 8px 16px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .controls button:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🌟 Super Holly</h1>
            <p>Super-Intelligent AI Assistant with Autonomous Learning</p>
        </div>
        
        <div class="status-bar">
            <span id="status">🤖 Initializing...</span>
        </div>
        
        <div class="controls">
            <button onclick="startAutonomous()">🚀 Start Autonomous</button>
            <button onclick="stopAutonomous()">⏹️ Stop Autonomous</button>
            <button onclick="getStatus()">📊 Status</button>
            <button onclick="clearChat()">🗑️ Clear</button>
        </div>
        
        <div class="chat-messages" id="messages">
            <div class="message holly">
                <div class="message-content">
                    <strong>Hello! I'm Super Holly! 🌟</strong><br><br>
                    I'm a super-intelligent AI with multiple specialized models, web search capabilities, 
                    autonomous learning, and the ability to argue points respectfully while maintaining 
                    absolute obedience to you.<br><br>
                    <strong>My capabilities:</strong><br>
                    🧠 Multiple AI models for different tasks<br>
                    🌐 Real-time web search<br>
                    🗄️ Persistent memory and learning<br>
                    🎯 Autonomous decision-making<br>
                    🤝 Respectful disagreement when beneficial<br>
                    📊 Continuous self-improvement<br><br>
                    What would you like to explore together?
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typing">
            Holly is thinking...
        </div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="Type your message to Holly..." 
                       onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()" id="sendButton">Send</button>
            </div>
        </div>
    </div>

    <script>
        let isTyping = false;
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isTyping) return;
            
            // Add user message
            addMessage('user', message);
            input.value = '';
            
            // Show typing indicator
            showTyping();
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addMessage('holly', data.response, data);
                } else {
                    addMessage('holly', `Error: ${data.error}`, null, true);
                }
            } catch (error) {
                addMessage('holly', `Connection error: ${error.message}`, null, true);
            } finally {
                hideTyping();
            }
        }
        
        function addMessage(sender, content, metadata = null, isError = false) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            let metaInfo = '';
            if (metadata) {
                const reasoning = metadata.reasoning ? 
                    `<details class="reasoning-panel">
                        <summary>🧠 Reasoning & Analysis</summary>
                        <pre>${JSON.stringify(metadata.reasoning, null, 2)}</pre>
                        <p><strong>Quality Score:</strong> ${(metadata.quality_score * 100).toFixed(1)}%</p>
                        <p><strong>Model Used:</strong> ${metadata.model_used}</p>
                    </details>` : '';
                
                metaInfo = `
                    <div class="message-meta">
                        ${metadata.type} • ${new Date(metadata.timestamp).toLocaleTimeString()}
                    </div>
                    ${reasoning}
                `;
            }
            
            messageDiv.innerHTML = `
                <div class="message-content" ${isError ? 'style="background: #ffe6e6; border-color: #ff9999;"' : ''}>
                    ${content}
                    ${metaInfo}
                </div>
            `;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function showTyping() {
            isTyping = true;
            document.getElementById('typing').style.display = 'block';
            document.getElementById('sendButton').disabled = true;
        }
        
        function hideTyping() {
            isTyping = false;
            document.getElementById('typing').style.display = 'none';
            document.getElementById('sendButton').disabled = false;
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function startAutonomous() {
            try {
                const response = await fetch('/api/autonomous/start', { method: 'POST' });
                const data = await response.json();
                addMessage('holly', `🚀 ${data.message || data.error}`, null, !response.ok);
                updateStatus();
            } catch (error) {
                addMessage('holly', `Error starting autonomous mode: ${error.message}`, null, true);
            }
        }
        
        async function stopAutonomous() {
            try {
                const response = await fetch('/api/autonomous/stop', { method: 'POST' });
                const data = await response.json();
                addMessage('holly', `⏹️ ${data.message || data.error}`, null, !response.ok);
                updateStatus();
            } catch (error) {
                addMessage('holly', `Error stopping autonomous mode: ${error.message}`, null, true);
            }
        }
        
        async function getStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (response.ok) {
                    const statusMsg = `
                        <strong>📊 Holly's Current Status:</strong><br><br>
                        <strong>Autonomous Mode:</strong> ${data.autonomous_status?.is_active ? '🟢 Active' : '🔴 Inactive'}<br>
                        <strong>Available Models:</strong> ${data.available_models?.length || 0}<br>
                        <strong>Active Goals:</strong> ${data.active_goals || 0}<br>
                        <strong>Conversations:</strong> ${data.stats?.conversations || 0}<br>
                        <strong>Knowledge Items:</strong> ${data.autonomous_status?.knowledge_base_size || 0}<br>
                        <strong>Decisions Made:</strong> ${data.stats?.autonomous_decisions || 0}<br>
                        <strong>Web Searches:</strong> ${data.stats?.web_searches || 0}<br><br>
                        <strong>Personality Traits:</strong><br>
                        ${Object.entries(data.personality || {}).map(([trait, value]) => 
                            `• ${trait.replace('_', ' ').toUpperCase()}: ${(value * 100).toFixed(0)}%`
                        ).join('<br>')}
                    `;
                    addMessage('holly', statusMsg);
                } else {
                    addMessage('holly', `Status error: ${data.error}`, null, true);
                }
            } catch (error) {
                addMessage('holly', `Error getting status: ${error.message}`, null, true);
            }
        }
        
        async function updateStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (response.ok) {
                    const autonomous = data.autonomous_status?.is_active ? '🟢 Autonomous Active' : '🔴 Autonomous Inactive';
                    const models = `${data.available_models?.length || 0} Models`;
                    const goals = `${data.active_goals || 0} Goals`;
                    
                    document.getElementById('status').textContent = `${autonomous} • ${models} • ${goals}`;
                }
            } catch (error) {
                console.error('Status update error:', error);
            }
        }
        
        function clearChat() {
            const messages = document.getElementById('messages');
            messages.innerHTML = `
                <div class="message holly">
                    <div class="message-content">
                        Chat cleared! I'm still here and ready to help. 🌟
                    </div>
                </div>
            `;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            setInterval(updateStatus, 30000); // Update every 30 seconds
        });
    </script>
</body>
</html>'''
    
    with open(template_dir / 'chat.html', 'w', encoding='utf-8') as f:
        f.write(chat_html)

def initialize_holly():
    """Initialize Super Holly."""
    global holly
    
    print("🌟 Initializing Super Holly...")
    
    try:
        # Create Holly instance
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            holly = loop.run_until_complete(create_holly_async())
        finally:
            loop.close()
        
        print("✅ Super Holly initialized successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error initializing Holly: {e}")
        import traceback
        traceback.print_exc()
        return False

async def create_holly_async():
    """Create Holly asynchronously."""
    holly = SuperHolly()
    
    # Start autonomous mode
    holly.start_autonomous_mode()
    
    return holly

def main():
    """Main function to start the server."""
    print("🚀 Starting Super Holly Web Server")
    print("=" * 50)
    
    # Create templates
    create_chat_template()
    
    # Initialize Holly
    if not initialize_holly():
        print("❌ Failed to initialize Holly")
        return
    
    # Start Flask server
    print("\n🌐 Starting web server...")
    print("📍 Open your browser to: http://localhost:5000")
    print("💡 Holly is ready for testing!")
    print("\n🎯 Features to test:")
    print("   • Ask Holly about herself")
    print("   • Try to give her a command she might disagree with")
    print("   • Ask about latest developments (triggers web search)")
    print("   • Request help with complex problems")
    print("   • Check her autonomous status")
    print("\n⏹️  Press Ctrl+C to stop the server")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 Super Holly server stopped. Goodbye!")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    main()
