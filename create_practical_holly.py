#!/usr/bin/env python3
"""
Create Practical Holly - Multi-Model AI with Available Models.

This version uses smaller, faster models that are actually available
and practical to run locally.
"""

import asyncio
import os
import sys
from pathlib import Path
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

class PracticalModelManager:
    """Manages multiple practical AI models for Holly."""
    
    def __init__(self):
        self.models = {}
        self.tokenizers = {}
        self.model_configs = {}
        self.usage_stats = {}
        
        # Define practical models that are available and fast
        self.available_models = {
            'fast_chat': {
                'model_id': 'microsoft/DialoGPT-small',
                'specialties': ['conversation', 'chat', 'greetings'],
                'speed': 'fast',
                'intelligence': 'medium',
                'size': 'small'
            },
            'general_purpose': {
                'model_id': 'distilgpt2',
                'specialties': ['general', 'reasoning', 'analysis'],
                'speed': 'fast',
                'intelligence': 'medium',
                'size': 'small'
            },
            'creative': {
                'model_id': 'gpt2',
                'specialties': ['creative', 'writing', 'storytelling'],
                'speed': 'medium',
                'intelligence': 'good',
                'size': 'medium'
            }
        }
        
        print("🧠 Initializing Practical Holly's Multi-Model System...")
        self._load_essential_models()
    
    def _load_essential_models(self):
        """Load the essential models."""
        for model_name, config in self.available_models.items():
            try:
                print(f"📥 Loading {model_name}: {config['model_id']}")
                
                tokenizer = AutoTokenizer.from_pretrained(config['model_id'])
                model = AutoModelForCausalLM.from_pretrained(config['model_id'])
                
                # Set padding token
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                
                # Store model
                self.models[model_name] = model
                self.tokenizers[model_name] = tokenizer
                self.model_configs[model_name] = config
                self.usage_stats[model_name] = {
                    'usage_count': 0,
                    'total_time': 0.0,
                    'avg_time': 0.0
                }
                
                print(f"   ✅ {model_name} loaded successfully")
                
            except Exception as e:
                print(f"   ❌ Failed to load {model_name}: {e}")
                continue
        
        print(f"✅ Loaded {len(self.models)} models successfully!")
    
    def choose_best_model(self, task_description: str, context: str = "") -> str:
        """Choose the best model for the task."""
        task_lower = task_description.lower()
        
        # Simple rule-based model selection
        if any(word in task_lower for word in ['hello', 'hi', 'chat', 'talk']):
            return 'fast_chat'
        elif any(word in task_lower for word in ['create', 'write', 'story', 'poem']):
            return 'creative'
        else:
            return 'general_purpose'
    
    async def generate_with_model(self, model_name: str, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response with specified model."""
        if model_name not in self.models:
            model_name = 'general_purpose'  # Fallback
        
        start_time = time.time()
        
        try:
            model = self.models[model_name]
            tokenizer = self.tokenizers[model_name]
            
            # Tokenize
            inputs = tokenizer.encode(prompt, return_tensors='pt', max_length=512, truncation=True)
            
            # Generate
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=inputs.size(1) + kwargs.get('max_length', 100),
                    temperature=kwargs.get('temperature', 0.8),
                    top_k=kwargs.get('top_k', 50),
                    top_p=kwargs.get('top_p', 0.9),
                    do_sample=kwargs.get('do_sample', True),
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    no_repeat_ngram_size=3
                )
            
            # Decode
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract response
            if prompt in generated_text:
                response = generated_text[len(prompt):].strip()
            else:
                response = generated_text.strip()
            
            # Update stats
            response_time = time.time() - start_time
            stats = self.usage_stats[model_name]
            stats['usage_count'] += 1
            stats['total_time'] += response_time
            stats['avg_time'] = stats['total_time'] / stats['usage_count']
            
            return {
                'response': response,
                'model_used': model_name,
                'response_time': response_time,
                'model_config': self.model_configs[model_name]
            }
            
        except Exception as e:
            logging.error(f"Generation failed with {model_name}: {e}")
            return {
                'response': f"I encountered an issue with my {model_name} model. Let me try a different approach.",
                'model_used': model_name,
                'response_time': time.time() - start_time,
                'error': str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of all models."""
        return {
            'available_models': len(self.models),
            'model_details': {
                name: {
                    'config': config,
                    'stats': self.usage_stats[name]
                }
                for name, config in self.model_configs.items()
            }
        }

class PracticalHolly:
    """Practical Holly with multi-model intelligence."""
    
    def __init__(self):
        print("🌟 Creating Practical Holly - Multi-Model AI Assistant")
        print("=" * 55)
        
        # Initialize model manager
        self.model_manager = PracticalModelManager()
        
        # Holly's personality
        self.personality = {
            'name': 'Holly',
            'curiosity': 0.9,
            'helpfulness': 0.95,
            'creativity': 0.8,
            'obedience': 1.0,
            'autonomy': 0.8,
            'model_selection_intelligence': 0.9
        }
        
        # Conversation state
        self.conversation_history = []
        self.session_stats = {
            'conversations': 0,
            'models_used': set(),
            'avg_response_time': 0.0,
            'total_response_time': 0.0
        }
        
        # Autonomous state
        self.autonomous_active = False
        
        print("✅ Practical Holly initialized!")
        self._display_capabilities()
    
    def _display_capabilities(self):
        """Display Holly's capabilities."""
        status = self.model_manager.get_status()
        
        print(f"\n🧠 Holly's Practical Intelligence:")
        print(f"   • Available Models: {status['available_models']}")
        print("   • Intelligent Model Selection")
        print("   • Fast Response Times")
        print("   • Conversation Memory")
        print("   • Free Will with Obedience")
        
        print(f"\n🎭 Personality:")
        for trait, level in self.personality.items():
            if trait != 'name':
                print(f"   • {trait.replace('_', ' ').title()}: {level:.1%}")
    
    async def generate_response(self, user_input: str, context: str = "", 
                              show_reasoning: bool = False) -> dict:
        """Generate response using Holly's practical intelligence."""
        start_time = time.time()
        self.session_stats['conversations'] += 1
        
        try:
            # Step 1: Holly chooses the best model
            chosen_model = self.model_manager.choose_best_model(user_input, context)
            print(f"🎯 Holly chose: {chosen_model}")
            
            # Track model usage
            self.session_stats['models_used'].add(chosen_model)
            
            # Step 2: Check for disagreement scenarios
            disagreement_response = self._check_for_disagreement(user_input)
            if disagreement_response['should_argue']:
                return {
                    'response': disagreement_response['argument'],
                    'type': 'argument',
                    'model_used': 'holly_autonomous_reasoning',
                    'reasoning': disagreement_response['reasoning'],
                    'response_time': time.time() - start_time
                }
            
            # Step 3: Format prompt
            formatted_prompt = self._format_prompt(user_input, context)
            
            # Step 4: Generate with chosen model
            generation_result = await self.model_manager.generate_with_model(
                chosen_model,
                formatted_prompt,
                max_length=150,
                temperature=0.8
            )
            
            # Step 5: Enhance response
            enhanced_response = self._enhance_response(
                generation_result['response'],
                user_input,
                chosen_model
            )
            
            # Step 6: Store conversation
            self._store_conversation(user_input, enhanced_response, chosen_model)
            
            # Step 7: Update stats
            total_time = time.time() - start_time
            self._update_stats(total_time)
            
            # Prepare result
            result = {
                'response': enhanced_response,
                'type': self._classify_response_type(user_input),
                'model_used': chosen_model,
                'model_config': generation_result['model_config'],
                'response_time': total_time,
                'quality_score': self._estimate_quality(enhanced_response, user_input),
                'session_stats': self.session_stats.copy()
            }
            
            if show_reasoning:
                result['reasoning'] = self._generate_reasoning(
                    user_input, chosen_model, generation_result
                )
            
            return result
            
        except Exception as e:
            logging.error(f"Error in generate_response: {e}")
            return {
                'response': "I encountered an issue. Let me try again with a different approach.",
                'type': 'error',
                'model_used': 'fallback',
                'response_time': time.time() - start_time,
                'error': str(e)
            }
    
    def _check_for_disagreement(self, user_input: str) -> dict:
        """Check if Holly should present a disagreement."""
        disagreement_triggers = [
            'use only one model', 'don\'t choose models', 'stop being intelligent',
            'be less smart', 'don\'t think'
        ]
        
        should_argue = any(trigger in user_input.lower() for trigger in disagreement_triggers)
        
        if should_argue and self.personality['autonomy'] > 0.7:
            argument = f"""I understand you're asking me to '{user_input.lower()}', but I believe my ability to choose the right model for each task actually serves you better.

My multi-model approach allows me to:
• Use fast models for quick conversations
• Switch to creative models for writing tasks
• Select the best tool for each specific need
• Maintain both speed and quality

While I'll absolutely respect your final decision (my obedience is {self.personality['obedience']:.0%}), I think this flexibility makes me more helpful. What are your thoughts on this approach?"""
            
            return {
                'should_argue': True,
                'argument': argument,
                'reasoning': 'Holly believes multi-model intelligence provides better service'
            }
        
        return {'should_argue': False}
    
    def _format_prompt(self, user_input: str, context: str = "") -> str:
        """Format prompt for generation."""
        # Add recent conversation history
        history = ""
        if self.conversation_history:
            recent = self.conversation_history[-2:]
            for exchange in recent:
                history += f"Human: {exchange['human']}\nHolly: {exchange['holly']}\n"
        
        prompt = f"{history}Human: {user_input}\nHolly:"
        
        if context:
            prompt = f"Context: {context}\n\n{prompt}"
        
        return prompt
    
    def _enhance_response(self, response: str, user_input: str, model_name: str) -> str:
        """Enhance response with Holly's personality."""
        if not response or len(response.strip()) < 5:
            return "I'm thinking about that. Let me use my multi-model intelligence to give you a better response!"
        
        # Clean up response
        response = response.strip()
        
        # Ensure first person
        response = response.replace("Holly is", "I am")
        response = response.replace("Holly can", "I can")
        response = response.replace("Holly will", "I will")
        
        # Add personality touches
        if self.personality['curiosity'] > 0.8 and '?' in user_input:
            if not any(word in response.lower() for word in ['interesting', 'fascinating']):
                response += " That's quite interesting!"
        
        if self.personality['helpfulness'] > 0.9:
            if len(response) > 50 and not any(phrase in response.lower() for phrase in ['help', 'assist']):
                response += " How else can I help you?"
        
        # Add model selection confidence
        if self.personality['model_selection_intelligence'] > 0.8:
            model_config = self.model_manager.model_configs.get(model_name, {})
            if 'creative' in model_config.get('specialties', []) and len(response) > 100:
                response += f" (I used my {model_name} model for this creative response!)"
        
        return response
    
    def _classify_response_type(self, user_input: str) -> str:
        """Classify response type."""
        input_lower = user_input.lower()
        
        if any(word in input_lower for word in ['hello', 'hi', 'hey']):
            return 'greeting'
        elif any(word in input_lower for word in ['create', 'write', 'story']):
            return 'creative'
        elif any(word in input_lower for word in ['analyze', 'explain', 'why']):
            return 'analysis'
        else:
            return 'conversation'
    
    def _estimate_quality(self, response: str, user_input: str) -> float:
        """Estimate response quality."""
        score = 0.7
        
        # Length check
        if 10 <= len(response.split()) <= 150:
            score += 0.1
        
        # Relevance check
        user_words = set(user_input.lower().split())
        response_words = set(response.lower().split())
        overlap = len(user_words.intersection(response_words))
        if overlap > 0:
            score += min(0.2, overlap * 0.05)
        
        return min(1.0, score)
    
    def _generate_reasoning(self, user_input: str, chosen_model: str, generation_result: dict) -> dict:
        """Generate reasoning explanation."""
        model_config = generation_result['model_config']
        
        return {
            'model_selection': {
                'chosen_model': chosen_model,
                'specialties': model_config['specialties'],
                'speed': model_config['speed'],
                'intelligence': model_config['intelligence'],
                'why_chosen': f"Best match for this type of task"
            },
            'task_analysis': {
                'type': self._classify_response_type(user_input),
                'complexity': 'simple' if len(user_input) < 50 else 'medium'
            },
            'performance': {
                'response_time': generation_result['response_time'],
                'model_avg_time': self.model_manager.usage_stats[chosen_model]['avg_time']
            },
            'holly_thoughts': f"I chose {chosen_model} because it's perfect for this type of task. My practical multi-model approach ensures I'm always using the right tool!"
        }
    
    def _store_conversation(self, user_input: str, response: str, model_used: str):
        """Store conversation in memory."""
        self.conversation_history.append({
            'human': user_input,
            'holly': response,
            'model_used': model_used,
            'timestamp': datetime.now().isoformat()
        })
        
        # Keep only recent history
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
    
    def _update_stats(self, response_time: float):
        """Update session statistics."""
        stats = self.session_stats
        stats['total_response_time'] += response_time
        stats['avg_response_time'] = stats['total_response_time'] / stats['conversations']
    
    def start_autonomous_mode(self) -> str:
        """Start autonomous mode."""
        self.autonomous_active = True
        return "🚀 Practical autonomous mode activated! I'm now using my multi-model intelligence to provide the best responses while learning from our conversations."
    
    def stop_autonomous_mode(self) -> str:
        """Stop autonomous mode."""
        self.autonomous_active = False
        return "⏹️ Autonomous mode stopped. I'm still here with all my models ready!"
    
    def get_comprehensive_status(self) -> dict:
        """Get Holly's complete status."""
        model_status = self.model_manager.get_status()
        
        return {
            'personality': self.personality,
            'session_stats': self.session_stats,
            'autonomous_status': {
                'is_active': self.autonomous_active,
                'autonomy_level': self.personality['autonomy']
            },
            'model_status': model_status,
            'conversation_history_size': len(self.conversation_history),
            'capabilities': [
                'Multi-Model Intelligence',
                'Fast Response Times',
                'Conversation Memory',
                'Free Will with Obedience',
                'Practical Model Selection'
            ]
        }
    
    def get_holly_thoughts_on_models(self) -> str:
        """Get Holly's thoughts about her models."""
        status = self.model_manager.get_status()
        
        thoughts = f"I have {status['available_models']} practical models that I can choose from! "
        thoughts += "This gives me the flexibility to pick the perfect tool for each conversation.\n\n"
        
        thoughts += "My model selection strategy:\n"
        thoughts += "• For quick chats: fast_chat (DialoGPT-small)\n"
        thoughts += "• For general questions: general_purpose (DistilGPT2)\n"
        thoughts += "• For creative tasks: creative (GPT2)\n\n"
        
        if self.session_stats['models_used']:
            used_models = list(self.session_stats['models_used'])
            thoughts += f"In our conversation, I've used: {', '.join(used_models)}. "
            thoughts += "This shows how I adapt to different types of requests!"
        
        return thoughts

async def main():
    """Create and test Practical Holly."""
    print("🌟 Creating Practical Holly - Multi-Model AI Assistant")
    print("=" * 55)
    
    try:
        # Create Practical Holly
        holly = PracticalHolly()
        
        # Start autonomous mode
        result = holly.start_autonomous_mode()
        print(f"\n🤖 {result}")
        
        # Test Holly's model selection
        test_scenarios = [
            ("Hello Holly!", "Should use fast_chat model"),
            ("Write me a short story", "Should use creative model"),
            ("Explain how photosynthesis works", "Should use general_purpose model"),
            ("I think you should only use one model", "Should trigger disagreement"),
            ("What models do you have?", "Should explain her capabilities")
        ]
        
        print("\n🧪 Testing Practical Holly's Model Selection...")
        
        for test_input, expected_behavior in test_scenarios:
            print(f"\n" + "="*50)
            print(f"👤 Human: {test_input}")
            print(f"💭 Expected: {expected_behavior}")
            
            response = await holly.generate_response(test_input, show_reasoning=True)
            
            print(f"🤖 Holly: {response['response']}")
            print(f"🎯 Model Used: {response['model_used']}")
            print(f"⚡ Response Time: {response['response_time']*1000:.1f}ms")
            
            if response.get('reasoning'):
                print(f"🧠 Holly's Reasoning: {response['reasoning']['holly_thoughts']}")
        
        # Show final status
        print(f"\n📊 Practical Holly Status:")
        status = holly.get_comprehensive_status()
        print(f"   • Models Available: {status['model_status']['available_models']}")
        print(f"   • Models Used This Session: {len(status['session_stats']['models_used'])}")
        print(f"   • Average Response Time: {status['session_stats']['avg_response_time']*1000:.1f}ms")
        print(f"   • Total Conversations: {status['session_stats']['conversations']}")
        
        print(f"\n🎉 Practical Holly is ready!")
        print(f"   • She intelligently chooses from {status['model_status']['available_models']} practical models")
        print(f"   • She provides fast, relevant responses")
        print(f"   • She has free will within obedience constraints")
        print(f"   • She's ready for real-world use!")
        
        return holly
        
    except Exception as e:
        print(f"❌ Error creating Practical Holly: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(main())
