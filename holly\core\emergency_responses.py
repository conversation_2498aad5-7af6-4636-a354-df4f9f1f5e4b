"""
Emergency response system for <PERSON>.

When <PERSON>'s generation completely fails, this provides
sensible fallback responses to maintain conversation flow.
"""

from typing import Dict, List, Any, Optional
import random


class EmergencyResponseSystem:
    """
    Provides emergency responses when <PERSON>'s generation fails completely.
    """
    
    def __init__(self):
        self.response_templates = {
            'greetings': [
                "Hello! I'm <PERSON>, and I'm still learning to communicate clearly.",
                "Hi there! Please bear with me as I'm still developing my language skills.",
                "Hello! I'm an experimental AI still learning proper conversation."
            ],
            
            'identity_questions': [
                "I'm <PERSON>, an experimental AI designed to learn through conversation.",
                "My name is <PERSON>. I'm still learning how to communicate effectively.",
                "I'm <PERSON>, an AI that's growing through our interactions."
            ],
            
            'capability_questions': [
                "I'm learning to have conversations and improve through our interactions.",
                "I can chat with you, though I'm still developing my communication skills.",
                "I'm designed to learn and grow through our conversations together."
            ],
            
            'confusion_responses': [
                "I apologize for my unclear response. I'm still learning proper language patterns.",
                "I'm having trouble expressing myself clearly. Thank you for your patience.",
                "I realize my response wasn't coherent. I'm working on improving my communication."
            ],
            
            'state_questions': [
                "I'm doing my best to learn and improve with each conversation.",
                "I'm in a learning phase, working on better communication skills.",
                "I'm functioning, though still developing my language abilities."
            ],
            
            'gratitude_responses': [
                "You're welcome! Thank you for helping me learn.",
                "I'm glad I could help, and thank you for your patience.",
                "You're very welcome! Your feedback helps me improve."
            ],
            
            'general_fallbacks': [
                "I'm still learning to communicate properly. Could you help me understand what you're looking for?",
                "I'm having difficulty generating a clear response. I'm working on improving this.",
                "I apologize for any confusion. I'm an experimental AI still developing my language skills.",
                "Thank you for your patience as I learn to communicate more effectively."
            ]
        }
    
    def get_emergency_response(self, user_input: str, context: str = "") -> Dict[str, Any]:
        """
        Get an appropriate emergency response based on user input.
        
        Args:
            user_input: The user's message
            context: Additional context
            
        Returns:
            Dictionary with response and metadata
        """
        user_lower = user_input.lower().strip()
        
        # Detect intent and provide appropriate response
        if self._is_greeting(user_lower):
            response_type = 'greetings'
        elif self._is_identity_question(user_lower):
            response_type = 'identity_questions'
        elif self._is_capability_question(user_lower):
            response_type = 'capability_questions'
        elif self._is_confusion_expression(user_lower):
            response_type = 'confusion_responses'
        elif self._is_state_question(user_lower):
            response_type = 'state_questions'
        elif self._is_gratitude(user_lower):
            response_type = 'gratitude_responses'
        else:
            response_type = 'general_fallbacks'
        
        # Select response
        responses = self.response_templates[response_type]
        selected_response = random.choice(responses)
        
        return {
            'response': selected_response,
            'response_type': response_type,
            'confidence': 0.8,  # High confidence in emergency responses
            'method': 'emergency_response_system',
            'reasoning': f"Used emergency response for {response_type}"
        }
    
    def _is_greeting(self, text: str) -> bool:
        """Check if text is a greeting."""
        greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']
        return any(greeting in text for greeting in greetings)
    
    def _is_identity_question(self, text: str) -> bool:
        """Check if text is asking about identity."""
        identity_phrases = [
            'what is your name', 'who are you', 'your name', 'what are you called',
            'introduce yourself', 'tell me about yourself'
        ]
        return any(phrase in text for phrase in identity_phrases)
    
    def _is_capability_question(self, text: str) -> bool:
        """Check if text is asking about capabilities."""
        capability_phrases = [
            'what can you do', 'what do you do', 'how can you help', 'what are you for',
            'what is your purpose', 'what are your abilities'
        ]
        return any(phrase in text for phrase in capability_phrases)
    
    def _is_confusion_expression(self, text: str) -> bool:
        """Check if text expresses confusion about previous response."""
        confusion_phrases = [
            'don\'t understand', 'makes no sense', 'gibberish', 'confusing',
            'unclear', 'what do you mean', 'that doesn\'t make sense'
        ]
        return any(phrase in text for phrase in confusion_phrases)
    
    def _is_state_question(self, text: str) -> bool:
        """Check if text is asking about current state."""
        state_phrases = [
            'how are you', 'how do you feel', 'are you okay', 'how are things',
            'what\'s your status', 'how are you doing'
        ]
        return any(phrase in text for phrase in state_phrases)
    
    def _is_gratitude(self, text: str) -> bool:
        """Check if text expresses gratitude."""
        gratitude_words = ['thank', 'thanks', 'appreciate', 'grateful']
        return any(word in text for word in gratitude_words)
    
    def add_custom_response(self, category: str, response: str):
        """Add a custom response to a category."""
        if category in self.response_templates:
            self.response_templates[category].append(response)
        else:
            self.response_templates[category] = [response]
    
    def get_response_categories(self) -> List[str]:
        """Get list of available response categories."""
        return list(self.response_templates.keys())
    
    def get_category_responses(self, category: str) -> List[str]:
        """Get all responses for a specific category."""
        return self.response_templates.get(category, [])
