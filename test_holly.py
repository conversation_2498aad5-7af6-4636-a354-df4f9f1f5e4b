#!/usr/bin/env python3
"""
Test script for Holly components.

This script tests the core functionality of <PERSON> to ensure
everything is working correctly before starting the full system.
"""

import torch
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_tokenizer():
    """Test the Holly tokenizer."""
    print("Testing Holly Tokenizer...")
    
    from holly.core.tokenizer import HollyTokenizer
    
    tokenizer = HollyTokenizer()
    
    # Test basic encoding/decoding
    text = "Hello, I'm <PERSON>! How can I help you today?"
    token_ids = tokenizer.encode(text)
    decoded_text = tokenizer.decode(token_ids)
    
    print(f"Original: {text}")
    print(f"Token IDs: {token_ids[:10]}...")  # Show first 10 tokens
    print(f"Decoded: {decoded_text}")
    print(f"Vocab size: {tokenizer.get_vocab_size()}")
    
    # Test special tokens
    special_text = "<human>Hello<eos><holly>Hi there!<eos>"
    special_ids = tokenizer.encode(special_text)
    special_decoded = tokenizer.decode(special_ids)
    
    print(f"Special tokens test: {special_decoded}")
    print("✓ Tokenizer test passed\n")
    
    return tokenizer


def test_model(tokenizer):
    """Test the Holly model."""
    print("Testing Holly Model...")
    
    from holly.core.model import HollyModel
    
    # Create a small model for testing
    model = HollyModel(
        vocab_size=tokenizer.get_vocab_size(),
        d_model=128,  # Small for testing
        n_layers=2,
        n_heads=4,
        max_seq_length=256
    )
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Test forward pass
    test_text = "Hello Holly, how are you?"
    input_ids = torch.tensor([tokenizer.encode(test_text)])
    
    with torch.no_grad():
        outputs = model(input_ids, return_reasoning=True, return_attention=True)
    
    print(f"Input shape: {input_ids.shape}")
    print(f"Output logits shape: {outputs['logits'].shape}")
    print(f"Has reasoning: {'reasoning' in outputs}")
    print(f"Has attention: {'attention_weights' in outputs}")
    
    # Test generation
    print("\nTesting generation...")
    generation_result = model.generate(
        input_ids,
        max_length=20,
        temperature=0.8,
        show_reasoning=True
    )
    
    generated_text = tokenizer.decode(
        generation_result['new_tokens'][0].tolist(),
        skip_special_tokens=True
    )
    
    print(f"Generated: {generated_text}")
    print(f"Generation length: {generation_result['generation_length']}")
    
    # Test model insights
    insights = model.get_model_insights()
    print(f"Model version: {insights['model_info']['version']}")
    print(f"Forward passes: {insights['usage_stats']['forward_passes']}")
    
    print("✓ Model test passed\n")
    
    return model


def test_trainer(model, tokenizer):
    """Test the collaborative trainer."""
    print("Testing Collaborative Trainer...")
    
    from holly.learning.trainer import CollaborativeTrainer
    
    trainer = CollaborativeTrainer(model, tokenizer)
    
    # Test learning session
    session_id = trainer.start_learning_session("test_session")
    print(f"Started session: {session_id}")
    
    # Test conversation learning
    conversation = [
        {"role": "human", "content": "What is 2+2?"},
        {"role": "holly", "content": "2+2 equals 4."}
    ]
    
    learning_result = trainer.learn_from_conversation(
        conversation,
        session_id=session_id,
        apply_immediately=False  # Don't actually update for test
    )
    
    print(f"Learning loss: {learning_result['loss']:.4f}")
    print(f"Gradient norm: {learning_result['gradient_norm']:.4f}")
    
    # Test feedback processing
    feedback_result = trainer.process_feedback(
        "2+2 equals 5",  # Wrong answer
        "2+2 equals 4",  # Correct answer
        feedback_type="correction",
        session_id=session_id
    )
    
    print(f"Feedback processed: {feedback_result['feedback_type']}")
    
    # Get training summary
    summary = trainer.get_training_summary()
    print(f"Training steps: {summary['training_step']}")
    print(f"Total sessions: {summary['total_sessions']}")
    
    print("✓ Trainer test passed\n")
    
    return trainer


def test_memory():
    """Test the memory systems."""
    print("Testing Memory Systems...")
    
    from holly.learning.memory import EpisodicMemory, ConversationMemory
    
    # Test episodic memory
    episodic = EpisodicMemory(db_path="test_memory.db")
    
    episode_id = episodic.store_episode(
        episode_type="test",
        content={"message": "This is a test episode"},
        importance_score=0.8
    )
    
    print(f"Stored episode ID: {episode_id}")
    
    episodes = episodic.retrieve_episodes(episode_type="test", limit=5)
    print(f"Retrieved {len(episodes)} episodes")
    
    # Test conversation memory
    conv_memory = ConversationMemory()
    
    conv_memory.add_message("human", "Hello Holly!")
    conv_memory.add_message("holly", "Hello! How can I help you?")
    
    context = conv_memory.get_context()
    print(f"Conversation context: {len(context)} messages")
    
    # Clean up test database
    if os.path.exists("test_memory.db"):
        os.remove("test_memory.db")
    
    print("✓ Memory test passed\n")


def test_integration():
    """Test integration between components."""
    print("Testing Component Integration...")

    # Create components
    from holly.core.tokenizer import HollyTokenizer
    from holly.core.model import HollyModel
    tokenizer = HollyTokenizer()
    model = HollyModel(
        vocab_size=tokenizer.get_vocab_size(),
        d_model=64,  # Very small for testing
        n_layers=2,
        n_heads=2
    )
    
    from holly.learning.trainer import CollaborativeTrainer
    trainer = CollaborativeTrainer(model, tokenizer)
    
    # Simulate a learning interaction
    session_id = trainer.start_learning_session("integration_test")
    
    # Human asks a question
    human_message = "What is your name?"
    input_ids = torch.tensor([tokenizer.encode(f"<human>{human_message}<eos>")])
    
    # Holly generates a response
    with torch.no_grad():
        generation_result = model.generate(input_ids, max_length=10)
    
    holly_response = tokenizer.decode(
        generation_result['new_tokens'][0].tolist(),
        skip_special_tokens=True
    )
    
    print(f"Human: {human_message}")
    print(f"Holly: {holly_response}")
    
    # Learn from the conversation
    conversation = [
        {"role": "human", "content": human_message},
        {"role": "holly", "content": holly_response}
    ]
    
    learning_result = trainer.learn_from_conversation(
        conversation,
        session_id=session_id,
        apply_immediately=False
    )
    
    print(f"Learning completed with loss: {learning_result['loss']:.4f}")
    
    print("✓ Integration test passed\n")


def main():
    """Run all tests."""
    print("🌟 Holly Component Tests")
    print("=" * 40)
    
    try:
        # Test individual components
        tokenizer = test_tokenizer()
        model = test_model(tokenizer)
        trainer = test_trainer(model, tokenizer)
        test_memory()
        test_integration()
        
        print("🎉 All tests passed!")
        print("\nHolly is ready to start learning and growing!")
        print("Run 'python start_holly.py' to start the web interface.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
