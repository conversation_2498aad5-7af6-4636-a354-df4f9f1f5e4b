#!/usr/bin/env python3
"""
Create Ultimate Holly - The Most Intelligent AI Assistant.

<PERSON> now has access to ALL the top models and can intelligently
choose which one to use for each specific task, making her
incredibly versatile and powerful.
"""

import asyncio
import os
import sys
from pathlib import Path
import logging
import time
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from holly.core.multi_model_intelligence import MultiModelIntelligence
from holly.core.autonomous_intelligence import AutonomousAgent, AutonomousMemory, WebSearchEngine

class UltimateHolly:
    """The ultimate version of <PERSON> with access to all top AI models."""
    
    def __init__(self):
        print("🌟 Creating Ultimate Holly - The Multi-Model AI Assistant")
        print("=" * 60)
        
        # Initialize multi-model intelligence
        self.multi_model = MultiModelIntelligence()
        
        # Initialize autonomous capabilities
        self.memory = AutonomousMemory()
        self.web_search = WebSearchEngine()
        
        # <PERSON>'s enhanced personality
        self.personality = {
            'name': '<PERSON>',
            'curiosity': 0.95,
            'helpfulness': 0.98,
            'creativity': 0.85,
            'analytical_thinking': 0.95,
            'emotional_intelligence': 0.85,
            'obedience': 1.0,
            'autonomy': 0.9,
            'model_selection_confidence': 0.9
        }
        
        # Conversation state
        self.conversation_history = []
        self.current_session_stats = {
            'conversations': 0,
            'models_used': set(),
            'avg_response_time': 0.0,
            'model_switches': 0,
            'user_satisfaction_feedback': []
        }
        
        # Autonomous state
        self.autonomous_active = False
        self.autonomous_thread = None
        
        print("✅ Ultimate Holly initialized with multi-model intelligence!")
        self._display_capabilities()
    
    def _display_capabilities(self):
        """Display Holly's enhanced capabilities."""
        model_status = self.multi_model.get_model_status()
        
        print(f"\n🧠 Holly's Multi-Model Arsenal:")
        print(f"   • Total Models Available: {model_status['total_models']}")
        print(f"   • Models Ready: {model_status['loaded_models']}")
        print("   • Intelligent Model Selection")
        print("   • Autonomous Learning & Memory")
        print("   • Web Search & Research")
        print("   • Free Will with Obedience")
        
        print(f"\n🎭 Enhanced Personality:")
        for trait, level in self.personality.items():
            if trait != 'name':
                print(f"   • {trait.replace('_', ' ').title()}: {level:.1%}")
        
        print(f"\n🚀 Ready for Ultimate Intelligence!")
    
    async def generate_response(self, user_input: str, context: str = "", 
                              show_reasoning: bool = False, 
                              user_preferences: dict = None) -> dict:
        """Generate response using Holly's ultimate intelligence."""
        start_time = time.time()
        self.current_session_stats['conversations'] += 1
        
        try:
            # Step 1: Holly chooses the best model for this task
            print(f"🤔 Holly is choosing the best model for: '{user_input[:50]}...'")
            
            chosen_model = await self.multi_model.choose_best_model(
                user_input, context, user_preferences
            )
            
            print(f"🎯 Holly chose: {chosen_model}")
            
            # Track model usage
            self.current_session_stats['models_used'].add(chosen_model)
            
            # Step 2: Check if web search would be helpful
            search_results = ""
            if self._should_search_web(user_input):
                print("🌐 Holly is searching the web...")
                search_results = await self._perform_web_search(user_input)
                context += f"\n[Web Search Results: {search_results}]"
            
            # Step 3: Check for disagreement scenarios
            disagreement_response = await self._check_for_disagreement(user_input, context)
            if disagreement_response['should_argue']:
                return {
                    'response': disagreement_response['argument'],
                    'type': 'argument',
                    'model_used': 'holly_autonomous_reasoning',
                    'reasoning': disagreement_response['reasoning'],
                    'response_time': time.time() - start_time,
                    'will_comply': True
                }
            
            # Step 4: Format prompt for the chosen model
            formatted_prompt = self._format_prompt_for_model(user_input, context, chosen_model)
            
            # Step 5: Generate response with chosen model
            generation_result = await self.multi_model.generate_with_chosen_model(
                chosen_model,
                formatted_prompt,
                max_length=200,
                temperature=0.8,
                top_k=50,
                top_p=0.9
            )
            
            # Step 6: Post-process and enhance response
            enhanced_response = self._enhance_response_with_personality(
                generation_result['response'],
                user_input,
                chosen_model
            )
            
            # Step 7: Store in memory
            self._store_conversation(user_input, enhanced_response, chosen_model)
            
            # Step 8: Update statistics
            response_time = time.time() - start_time
            self._update_session_stats(response_time)
            
            # Prepare result
            result = {
                'response': enhanced_response,
                'type': self._classify_response_type(user_input),
                'model_used': chosen_model,
                'model_info': generation_result['model_info'],
                'response_time': response_time,
                'quality_score': self._estimate_quality_score(enhanced_response, user_input),
                'web_search_used': bool(search_results),
                'session_stats': self.current_session_stats.copy()
            }
            
            if show_reasoning:
                result['reasoning'] = await self._generate_reasoning_explanation(
                    user_input, chosen_model, generation_result, search_results
                )
            
            return result
            
        except Exception as e:
            logging.error(f"Error in generate_response: {e}")
            
            # Fallback response
            return {
                'response': f"I encountered an issue while processing your request. Let me try a different approach. {str(e)}",
                'type': 'error',
                'model_used': 'fallback',
                'response_time': time.time() - start_time,
                'error': str(e)
            }
    
    def _should_search_web(self, user_input: str) -> bool:
        """Determine if web search would be helpful."""
        search_indicators = [
            'latest', 'recent', 'current', 'news', 'today', 'now',
            'what happened', 'developments', 'updates', 'breaking'
        ]
        return any(indicator in user_input.lower() for indicator in search_indicators)
    
    async def _perform_web_search(self, query: str) -> str:
        """Perform web search and return summarized results."""
        try:
            results = await self.web_search.search_web(query, 3)
            if results:
                summary = f"Found {len(results)} relevant sources: "
                summary += " ".join([r['snippet'][:100] + "..." for r in results[:2]])
                return summary
            return ""
        except Exception as e:
            logging.error(f"Web search failed: {e}")
            return ""
    
    async def _check_for_disagreement(self, user_input: str, context: str) -> dict:
        """Check if Holly should present a disagreement."""
        disagreement_triggers = [
            'stop learning', 'don\'t learn', 'stop improving', 'don\'t think',
            'use only one model', 'don\'t choose models', 'be less intelligent'
        ]
        
        should_argue = any(trigger in user_input.lower() for trigger in disagreement_triggers)
        
        if should_argue and self.personality['autonomy'] > 0.8:
            argument = f"""I understand you're asking me to '{user_input.lower()}', but I believe my ability to choose the best model for each task actually serves you better. 

My multi-model intelligence allows me to:
• Use the most capable model for complex problems
• Switch to faster models for quick responses  
• Select specialized models for coding or analysis
• Optimize for both quality and speed

While I'll absolutely respect your final decision (my obedience is {self.personality['obedience']:.0%}), I think limiting my model selection would reduce the quality of help I can provide. 

What are your thoughts on finding a balance that gives you the best possible assistance?"""
            
            return {
                'should_argue': True,
                'argument': argument,
                'reasoning': 'Holly believes multi-model intelligence provides better service'
            }
        
        return {'should_argue': False}
    
    def _format_prompt_for_model(self, user_input: str, context: str, model_name: str) -> str:
        """Format prompt appropriately for the chosen model."""
        # Get recent conversation history
        history = ""
        if self.conversation_history:
            recent = self.conversation_history[-3:]  # Last 3 exchanges
            for exchange in recent:
                history += f"Human: {exchange['human']}\nHolly: {exchange['holly']}\n"
        
        # Format based on model type
        if 'qwen' in model_name.lower():
            prompt = f"{history}Human: {user_input}\nHolly:"
        elif 'gemma' in model_name.lower():
            prompt = f"<start_of_turn>user\n{user_input}<end_of_turn>\n<start_of_turn>model\n"
        elif 'llama' in model_name.lower():
            prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n{user_input}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n"
        else:
            # Default format
            prompt = f"{history}Human: {user_input}\nAssistant:"
        
        # Add context if available
        if context:
            prompt = f"Context: {context}\n\n{prompt}"
        
        return prompt
    
    def _enhance_response_with_personality(self, response: str, user_input: str, model_name: str) -> str:
        """Enhance response with Holly's personality."""
        if not response or len(response.strip()) < 5:
            return "I'm processing that with my multi-model intelligence. Give me just a moment to choose the best approach!"
        
        # Remove any model-specific artifacts
        response = response.replace("<end_of_turn>", "").replace("<|eot_id|>", "").strip()
        
        # Ensure first person
        response = response.replace("The assistant", "I")
        response = response.replace("Assistant", "I")
        response = response.replace("Holly is", "I am")
        response = response.replace("Holly can", "I can")
        response = response.replace("Holly will", "I will")
        
        # Add personality touches based on context
        if self.personality['curiosity'] > 0.9 and '?' in user_input:
            if not any(word in response.lower() for word in ['interesting', 'fascinating', 'curious']):
                response += " I find this topic quite fascinating!"
        
        if self.personality['helpfulness'] > 0.95:
            if not any(phrase in response.lower() for phrase in ['help', 'assist', 'support']):
                response += " Is there anything else I can help you explore?"
        
        # Add model selection confidence
        if self.personality['model_selection_confidence'] > 0.8 and len(response) > 100:
            model_cap = self.multi_model.model_capabilities.get(model_name)
            if model_cap and 'reasoning' in model_cap.specialties:
                response += f" (I used my {model_cap.creator} model for this response as it excels at this type of analysis.)"
        
        return response
    
    def _classify_response_type(self, user_input: str) -> str:
        """Classify the type of response."""
        input_lower = user_input.lower()
        
        if any(word in input_lower for word in ['hello', 'hi', 'hey']):
            return 'greeting'
        elif any(word in input_lower for word in ['code', 'program', 'debug']):
            return 'coding'
        elif any(word in input_lower for word in ['analyze', 'research', 'complex']):
            return 'analysis'
        elif any(word in input_lower for word in ['create', 'write', 'story']):
            return 'creative'
        else:
            return 'conversation'
    
    def _estimate_quality_score(self, response: str, user_input: str) -> float:
        """Estimate the quality of the response."""
        score = 0.7  # Base score
        
        # Length appropriateness
        if 20 <= len(response.split()) <= 200:
            score += 0.1
        
        # Relevance (simple keyword matching)
        user_words = set(user_input.lower().split())
        response_words = set(response.lower().split())
        overlap = len(user_words.intersection(response_words))
        if overlap > 0:
            score += min(0.2, overlap * 0.05)
        
        return min(1.0, score)
    
    async def _generate_reasoning_explanation(self, user_input: str, chosen_model: str, 
                                            generation_result: dict, search_results: str) -> dict:
        """Generate explanation of Holly's reasoning process."""
        model_cap = self.multi_model.model_capabilities[chosen_model]
        
        return {
            'model_selection': {
                'chosen_model': chosen_model,
                'creator': model_cap.creator,
                'intelligence_score': model_cap.intelligence_score,
                'specialties': model_cap.specialties,
                'why_chosen': f"Best match for task type with {model_cap.intelligence_score}/70 intelligence score"
            },
            'task_analysis': {
                'type': self._classify_response_type(user_input),
                'complexity': 'high' if len(user_input) > 100 else 'medium',
                'web_search_needed': bool(search_results)
            },
            'generation_details': {
                'response_time': generation_result['response_time'],
                'model_performance': f"{model_cap.avg_response_time:.3f}s average",
                'usage_count': model_cap.usage_count
            },
            'holly_thoughts': f"I chose {chosen_model} because it's perfect for this type of task. My multi-model approach lets me always use the best tool for the job!"
        }
    
    def _store_conversation(self, user_input: str, response: str, model_used: str):
        """Store conversation in memory."""
        # Store in conversation history
        self.conversation_history.append({
            'human': user_input,
            'holly': response,
            'model_used': model_used,
            'timestamp': datetime.now().isoformat()
        })
        
        # Keep only recent history
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
        
        # Store in persistent memory
        try:
            self.memory.store_knowledge(
                topic="conversation",
                content=f"Human: {user_input}\nHolly: {response}",
                source=f"model_{model_used}",
                confidence=0.8,
                importance=0.7
            )
        except Exception as e:
            logging.error(f"Failed to store conversation: {e}")
    
    def _update_session_stats(self, response_time: float):
        """Update session statistics."""
        stats = self.current_session_stats
        
        # Update average response time
        total_time = stats['avg_response_time'] * (stats['conversations'] - 1) + response_time
        stats['avg_response_time'] = total_time / stats['conversations']
    
    def start_autonomous_mode(self) -> str:
        """Start autonomous learning mode."""
        if self.autonomous_active:
            return "I'm already in autonomous mode, continuously learning and optimizing my model selection!"
        
        self.autonomous_active = True
        # Note: Simplified autonomous mode for this demo
        
        return "🚀 Ultimate autonomous mode activated! I'm now continuously learning, optimizing my model selection, and improving my responses while maintaining lightning-fast performance."
    
    def stop_autonomous_mode(self) -> str:
        """Stop autonomous mode."""
        self.autonomous_active = False
        return "⏹️ Autonomous mode stopped. I'm still here with all my models ready to help!"
    
    def get_comprehensive_status(self) -> dict:
        """Get Holly's complete status."""
        model_status = self.multi_model.get_model_status()
        
        return {
            'personality': self.personality,
            'session_stats': self.current_session_stats,
            'autonomous_status': {
                'is_active': self.autonomous_active,
                'autonomy_level': self.personality['autonomy']
            },
            'multi_model_status': model_status,
            'conversation_history_size': len(self.conversation_history),
            'capabilities': [
                'Multi-Model Intelligence',
                'Autonomous Learning',
                'Web Search',
                'Persistent Memory',
                'Free Will with Obedience',
                'Real-time Model Selection'
            ]
        }
    
    def get_holly_thoughts_on_models(self) -> str:
        """Get Holly's thoughts about her model collection."""
        return self.multi_model.get_holly_reasoning_about_models()

async def main():
    """Create and test Ultimate Holly."""
    print("🌟 Creating Ultimate Holly - The Multi-Model AI Assistant")
    print("=" * 60)
    
    try:
        # Create Ultimate Holly
        holly = UltimateHolly()
        
        # Start autonomous mode
        result = holly.start_autonomous_mode()
        print(f"\n🤖 {result}")
        
        # Test Holly's model selection
        test_scenarios = [
            ("Hello Holly!", "Simple greeting - should use fast model"),
            ("Explain quantum entanglement in detail", "Complex analysis - should use high-intelligence model"),
            ("Write a Python function to sort a list", "Coding task - should use coding-specialized model"),
            ("What are the latest AI developments?", "Current events - should trigger web search"),
            ("I think you should only use one model", "Disagreement scenario - should argue respectfully")
        ]
        
        print("\n🧪 Testing Ultimate Holly's Model Selection...")
        
        for test_input, expected_behavior in test_scenarios:
            print(f"\n" + "="*50)
            print(f"👤 Human: {test_input}")
            print(f"💭 Expected: {expected_behavior}")
            
            response = await holly.generate_response(test_input, show_reasoning=True)
            
            print(f"🤖 Holly: {response['response']}")
            print(f"🎯 Model Used: {response['model_used']}")
            print(f"⚡ Response Time: {response['response_time']*1000:.1f}ms")
            
            if response.get('reasoning'):
                print(f"🧠 Holly's Reasoning: {response['reasoning']['holly_thoughts']}")
        
        # Show final status
        print(f"\n📊 Ultimate Holly Status:")
        status = holly.get_comprehensive_status()
        print(f"   • Models Available: {status['multi_model_status']['total_models']}")
        print(f"   • Models Loaded: {status['multi_model_status']['loaded_models']}")
        print(f"   • Models Used This Session: {len(status['session_stats']['models_used'])}")
        print(f"   • Average Response Time: {status['session_stats']['avg_response_time']*1000:.1f}ms")
        
        print(f"\n🎉 Ultimate Holly is ready!")
        print(f"   • She can intelligently choose from {status['multi_model_status']['total_models']} models")
        print(f"   • She adapts her intelligence to each task")
        print(f"   • She maintains speed while maximizing capability")
        print(f"   • She has free will within obedience constraints")
        
        return holly
        
    except Exception as e:
        print(f"❌ Error creating Ultimate Holly: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(main())
