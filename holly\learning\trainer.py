"""
Collaborative trainer for <PERSON>.

This module implements the training system that allows <PERSON> to learn
from conversations and feedback in real-time, with human oversight
and collaborative guidance.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json
import logging

from holly.core.model import HollyModel
from holly.core.tokenizer import HollyTokenizer


class CollaborativeTrainer:
    """
    Trainer for collaborative human-AI learning.
    
    Features:
    - Real-time learning from conversations
    - Human feedback integration
    - Safe incremental updates
    - Learning session management
    - Performance monitoring
    """
    
    def __init__(
        self,
        model: HollyModel,
        tokenizer: HollyTokenizer,
        learning_rate: float = 1e-4,
        max_grad_norm: float = 1.0,
        warmup_steps: int = 100,
        safety_checks: bool = True
    ):
        self.model = model
        self.tokenizer = tokenizer
        self.learning_rate = learning_rate
        self.max_grad_norm = max_grad_norm
        self.warmup_steps = warmup_steps
        self.safety_checks = safety_checks
        
        # Optimizer setup
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=0.01,
            betas=(0.9, 0.95)
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=warmup_steps,
            T_mult=2
        )
        
        # Training state
        self.training_step = 0
        self.learning_sessions = []
        self.feedback_history = []
        
        # Safety monitoring
        self.safety_metrics = {
            'gradient_explosions': 0,
            'loss_spikes': 0,
            'parameter_drift': 0,
            'last_safe_state': None
        }
        
        # Setup logging
        self.logger = logging.getLogger('holly.trainer')
    
    def start_learning_session(self, session_name: str = None) -> str:
        """Start a new collaborative learning session."""
        session_id = session_name or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        session_info = {
            'session_id': session_id,
            'start_time': datetime.now().isoformat(),
            'initial_model_state': self.model.get_model_insights(),
            'learning_objectives': [],
            'feedback_received': [],
            'performance_metrics': []
        }
        
        self.learning_sessions.append(session_info)
        self.logger.info(f"Started learning session: {session_id}")
        
        return session_id
    
    def learn_from_conversation(
        self,
        conversation: List[Dict[str, str]],
        session_id: str = None,
        apply_immediately: bool = True
    ) -> Dict[str, Any]:
        """
        Learn from a conversation between human and Holly.
        
        Args:
            conversation: List of messages with 'role' and 'content'
            session_id: Optional session identifier
            apply_immediately: Whether to apply updates immediately
            
        Returns:
            Learning results and statistics
        """
        self.model.train()
        
        # Prepare training data from conversation
        input_ids, target_ids = self._prepare_conversation_data(conversation)
        
        if input_ids is None or target_ids is None:
            return {'error': 'Could not prepare training data from conversation'}
        
        # Forward pass
        outputs = self.model(input_ids, return_reasoning=True)
        logits = outputs['logits']
        
        # Calculate loss
        loss = nn.functional.cross_entropy(
            logits.view(-1, logits.size(-1)),
            target_ids.view(-1),
            ignore_index=-100
        )
        
        # Safety check
        if self.safety_checks and not self._is_safe_to_update(loss):
            return {
                'status': 'blocked',
                'reason': 'Safety check failed',
                'loss': float(loss),
                'applied': False
            }
        
        # Backward pass
        self.optimizer.zero_grad()
        loss.backward()
        
        # Gradient clipping
        grad_norm = torch.nn.utils.clip_grad_norm_(
            self.model.parameters(), 
            self.max_grad_norm
        )
        
        # Apply updates if requested
        if apply_immediately:
            self.optimizer.step()
            self.scheduler.step()
            self.training_step += 1
        
        # Record learning statistics
        learning_stats = {
            'training_step': self.training_step,
            'loss': float(loss),
            'gradient_norm': float(grad_norm),
            'learning_rate': self.optimizer.param_groups[0]['lr'],
            'conversation_length': len(conversation),
            'tokens_processed': input_ids.size(1),
            'applied': apply_immediately,
            'timestamp': datetime.now().isoformat()
        }
        
        # Add to session if specified
        if session_id:
            self._update_session(session_id, 'conversation_learning', learning_stats)
        
        self.logger.info(f"Learned from conversation: loss={loss:.4f}, grad_norm={grad_norm:.4f}")
        
        return learning_stats
    
    def process_feedback(
        self,
        original_response: str,
        corrected_response: str,
        feedback_type: str = "correction",
        session_id: str = None
    ) -> Dict[str, Any]:
        """
        Process human feedback and learn from corrections.
        
        Args:
            original_response: Holly's original response
            corrected_response: Human-corrected version
            feedback_type: Type of feedback ("correction", "preference", "guidance")
            session_id: Optional session identifier
            
        Returns:
            Feedback processing results
        """
        # Tokenize both responses
        original_ids = torch.tensor([self.tokenizer.encode(original_response)])
        corrected_ids = torch.tensor([self.tokenizer.encode(corrected_response)])
        
        # Learn from the correction
        learning_result = self.model.learn_from_feedback(
            original_ids,
            corrected_ids,
            feedback_type=feedback_type,
            learning_rate=self.learning_rate * 0.5  # Use lower LR for feedback
        )
        
        # Record feedback
        feedback_record = {
            'original_response': original_response,
            'corrected_response': corrected_response,
            'feedback_type': feedback_type,
            'learning_result': learning_result,
            'timestamp': datetime.now().isoformat()
        }
        
        self.feedback_history.append(feedback_record)
        
        # Add to session if specified
        if session_id:
            self._update_session(session_id, 'feedback', feedback_record)
        
        self.logger.info(f"Processed {feedback_type} feedback: loss={learning_result['loss']:.4f}")
        
        return feedback_record
    
    def _prepare_conversation_data(
        self, 
        conversation: List[Dict[str, str]]
    ) -> Tuple[Optional[torch.Tensor], Optional[torch.Tensor]]:
        """Prepare conversation data for training."""
        if not conversation:
            return None, None
        
        # Build conversation text with special tokens
        conversation_text = ""
        for message in conversation:
            role = message.get('role', 'unknown')
            content = message.get('content', '')
            
            if role == 'human':
                conversation_text += f"<human>{content}<eos>"
            elif role == 'holly':
                conversation_text += f"<holly>{content}<eos>"
        
        # Tokenize
        token_ids = self.tokenizer.encode(conversation_text)
        
        if len(token_ids) < 2:
            return None, None
        
        # Create input and target sequences
        input_ids = torch.tensor([token_ids[:-1]])
        target_ids = torch.tensor([token_ids[1:]])
        
        return input_ids, target_ids
    
    def _is_safe_to_update(self, loss: torch.Tensor) -> bool:
        """Check if it's safe to apply the current update."""
        if not self.safety_checks:
            return True
        
        # Check for loss spikes
        if len(self.feedback_history) > 0:
            recent_losses = [f['learning_result']['loss'] for f in self.feedback_history[-10:]]
            if recent_losses:
                avg_recent_loss = sum(recent_losses) / len(recent_losses)
                if float(loss) > avg_recent_loss * 3:  # Loss spike threshold
                    self.safety_metrics['loss_spikes'] += 1
                    return False
        
        # Check for gradient explosion
        total_grad_norm = 0.0
        for param in self.model.parameters():
            if param.grad is not None:
                total_grad_norm += param.grad.data.norm(2).item() ** 2
        total_grad_norm = total_grad_norm ** 0.5
        
        if total_grad_norm > self.max_grad_norm * 5:  # Severe gradient explosion
            self.safety_metrics['gradient_explosions'] += 1
            return False
        
        return True
    
    def _update_session(self, session_id: str, event_type: str, data: Dict[str, Any]):
        """Update learning session with new event."""
        for session in self.learning_sessions:
            if session['session_id'] == session_id:
                if event_type not in session:
                    session[event_type] = []
                session[event_type].append(data)
                break
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get comprehensive training summary."""
        return {
            'training_step': self.training_step,
            'learning_rate': self.optimizer.param_groups[0]['lr'],
            'total_sessions': len(self.learning_sessions),
            'total_feedback': len(self.feedback_history),
            'safety_metrics': self.safety_metrics,
            'recent_performance': self._calculate_recent_performance(),
            'model_evolution': self._track_model_evolution()
        }
    
    def _calculate_recent_performance(self) -> Dict[str, float]:
        """Calculate recent training performance metrics."""
        if not self.feedback_history:
            return {}
        
        recent_feedback = self.feedback_history[-20:]
        recent_losses = [f['learning_result']['loss'] for f in recent_feedback]
        
        return {
            'avg_loss': sum(recent_losses) / len(recent_losses),
            'loss_trend': recent_losses[-5:] if len(recent_losses) >= 5 else recent_losses,
            'feedback_types': {
                ftype: sum(1 for f in recent_feedback if f['feedback_type'] == ftype)
                for ftype in set(f['feedback_type'] for f in recent_feedback)
            }
        }
    
    def _track_model_evolution(self) -> Dict[str, Any]:
        """Track how the model has evolved during training."""
        if not self.learning_sessions:
            return {}
        
        first_session = self.learning_sessions[0]
        current_insights = self.model.get_model_insights()
        
        return {
            'parameter_changes': 'tracked',  # Placeholder for parameter drift analysis
            'capability_growth': 'measured',  # Placeholder for capability assessment
            'learning_efficiency': self.training_step / max(len(self.feedback_history), 1)
        }
