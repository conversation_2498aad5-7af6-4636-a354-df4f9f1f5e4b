"""
Self-improvement tools for <PERSON>.

This module provides <PERSON> with tools to analyze her own responses,
detect when she's not communicating well, and make real-time adjustments
to improve her language generation.
"""

import torch
import torch.nn.functional as F
from typing import Dict, List, Any, Optional, Tuple
import re
import string
from datetime import datetime
from holly.core.emergency_responses import EmergencyResponseSystem


class SelfImprovementTools:
    """
    Tools for <PERSON> to analyze and improve her own responses.
    """
    
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.response_history = []
        self.improvement_attempts = []
        self.emergency_system = EmergencyResponseSystem()
        
    def analyze_response_quality(self, response: str, context: str = "") -> Dict[str, Any]:
        """
        Analyze the quality of <PERSON>'s response to detect issues.
        
        Returns:
            Analysis with quality scores and detected issues
        """
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'response': response,
            'context': context,
            'quality_scores': {},
            'detected_issues': [],
            'improvement_suggestions': []
        }
        
        # Check for gibberish/random characters
        gibberish_score = self._detect_gibberish(response)
        analysis['quality_scores']['gibberish'] = gibberish_score
        
        if gibberish_score > 0.7:
            analysis['detected_issues'].append('high_gibberish')
            analysis['improvement_suggestions'].append('reduce_temperature')
            analysis['improvement_suggestions'].append('use_top_k_filtering')
        
        # Check for repetition
        repetition_score = self._detect_repetition(response)
        analysis['quality_scores']['repetition'] = repetition_score
        
        if repetition_score > 0.5:
            analysis['detected_issues'].append('excessive_repetition')
            analysis['improvement_suggestions'].append('increase_temperature')
            analysis['improvement_suggestions'].append('use_repetition_penalty')
        
        # Check for coherence
        coherence_score = self._assess_coherence(response)
        analysis['quality_scores']['coherence'] = coherence_score
        
        if coherence_score < 0.3:
            analysis['detected_issues'].append('low_coherence')
            analysis['improvement_suggestions'].append('adjust_generation_parameters')
        
        # Check for appropriate length
        length_score = self._assess_length(response, context)
        analysis['quality_scores']['length'] = length_score
        
        if length_score < 0.5:
            analysis['detected_issues'].append('inappropriate_length')
            analysis['improvement_suggestions'].append('adjust_max_length')
        
        # Overall quality assessment
        overall_quality = (
            (1 - gibberish_score) * 0.4 +
            (1 - repetition_score) * 0.2 +
            coherence_score * 0.3 +
            length_score * 0.1
        )
        analysis['quality_scores']['overall'] = overall_quality
        
        return analysis
    
    def _detect_gibberish(self, text: str) -> float:
        """Detect if text contains gibberish or random characters."""
        if not text:
            return 1.0
        
        # Count non-alphabetic characters (excluding common punctuation)
        allowed_chars = set(string.ascii_letters + string.digits + ' .,!?;:-()[]{}"\'\n\t')
        weird_chars = sum(1 for c in text if c not in allowed_chars)
        weird_ratio = weird_chars / len(text)
        
        # Check for random character sequences
        random_sequences = 0
        words = text.split()
        for word in words:
            if len(word) > 3:
                # Check if word has too many consonants in a row
                consonant_runs = re.findall(r'[bcdfghjklmnpqrstvwxyzBCDFGHJKLMNPQRSTVWXYZ]{4,}', word)
                if consonant_runs:
                    random_sequences += 1
                
                # Check for alternating case or numbers mixed with letters randomly
                if re.search(r'[a-z][A-Z][a-z]|[A-Za-z]\d[A-Za-z]|\d[A-Za-z]\d', word):
                    random_sequences += 1
        
        if words:
            random_ratio = random_sequences / len(words)
        else:
            random_ratio = 0
        
        # Combine metrics
        gibberish_score = min(1.0, weird_ratio * 2 + random_ratio)
        return gibberish_score
    
    def _detect_repetition(self, text: str) -> float:
        """Detect excessive repetition in text."""
        if not text:
            return 0.0
        
        words = text.split()
        if len(words) < 2:
            return 0.0
        
        # Count repeated words
        word_counts = {}
        for word in words:
            word_lower = word.lower().strip('.,!?;:')
            word_counts[word_lower] = word_counts.get(word_lower, 0) + 1
        
        # Calculate repetition score
        total_words = len(words)
        repeated_words = sum(count - 1 for count in word_counts.values() if count > 1)
        repetition_score = repeated_words / total_words if total_words > 0 else 0
        
        # Check for character repetition
        char_repetition = len(re.findall(r'(.)\1{3,}', text))  # 4+ repeated chars
        char_score = min(1.0, char_repetition / 10)
        
        return max(repetition_score, char_score)
    
    def _assess_coherence(self, text: str) -> float:
        """Assess if text forms coherent sentences."""
        if not text:
            return 0.0
        
        # Basic coherence indicators
        coherence_score = 0.5  # Start with neutral
        
        # Check for proper sentence structure
        sentences = re.split(r'[.!?]+', text)
        valid_sentences = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 2:
                # Check if sentence has reasonable word structure
                words = sentence.split()
                if len(words) >= 2:
                    # Simple heuristic: sentences with 2+ words are more likely coherent
                    valid_sentences += 1
        
        if sentences:
            sentence_coherence = valid_sentences / len(sentences)
            coherence_score = sentence_coherence
        
        # Check for common English patterns
        common_patterns = [
            r'\b(i|I)\s+(am|was|will|can|have|do)\b',  # "I am", "I was", etc.
            r'\b(you|You)\s+(are|were|will|can|have|do)\b',  # "You are", etc.
            r'\b(the|The)\s+\w+',  # "The word"
            r'\b(a|A|an|An)\s+\w+',  # "A word", "An apple"
            r'\b(is|are|was|were)\s+\w+',  # "is good", "are nice"
        ]
        
        pattern_matches = 0
        for pattern in common_patterns:
            if re.search(pattern, text):
                pattern_matches += 1
        
        pattern_score = min(1.0, pattern_matches / 3)  # Normalize
        
        # Combine scores
        final_coherence = (coherence_score + pattern_score) / 2
        return final_coherence
    
    def _assess_length(self, text: str, context: str = "") -> float:
        """Assess if response length is appropriate."""
        if not text:
            return 0.0
        
        words = text.split()
        word_count = len(words)
        
        # Ideal response length depends on context
        if "what" in context.lower() or "how" in context.lower():
            # Questions typically need longer answers
            ideal_range = (3, 20)
        else:
            # Simple greetings can be shorter
            ideal_range = (1, 15)
        
        min_words, max_words = ideal_range
        
        if word_count < min_words:
            return word_count / min_words  # Too short
        elif word_count > max_words:
            return max_words / word_count  # Too long
        else:
            return 1.0  # Just right
    
    def suggest_generation_improvements(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Suggest specific improvements to generation parameters based on analysis.
        """
        current_issues = analysis['detected_issues']
        suggestions = analysis['improvement_suggestions']

        improvements = {
            'parameter_adjustments': {},
            'reasoning': [],
            'priority': 'medium',
            'fallback_response': None
        }

        if 'high_gibberish' in current_issues:
            # For severe gibberish, use very conservative settings
            improvements['parameter_adjustments']['temperature'] = 0.1  # Extremely low
            improvements['parameter_adjustments']['top_k'] = 3  # Very restrictive
            improvements['parameter_adjustments']['top_p'] = 0.3  # Highly focused
            improvements['parameter_adjustments']['do_sample'] = False  # Use greedy decoding
            improvements['reasoning'].append("Detected severe gibberish - using greedy decoding")
            improvements['priority'] = 'critical'

            # Provide fallback responses for common inputs
            improvements['fallback_response'] = self._get_fallback_response(analysis.get('context', ''))

        elif 'low_coherence' in current_issues:
            improvements['parameter_adjustments']['temperature'] = 0.2  # Very low
            improvements['parameter_adjustments']['top_k'] = 5
            improvements['parameter_adjustments']['top_p'] = 0.5
            improvements['reasoning'].append("Low coherence detected - using conservative generation")
            improvements['priority'] = 'high'

        if 'excessive_repetition' in current_issues:
            improvements['parameter_adjustments']['repetition_penalty'] = 1.3
            improvements['reasoning'].append("Repetition detected - adding penalty")

        if 'inappropriate_length' in current_issues:
            if analysis['quality_scores']['length'] < 0.3:
                improvements['parameter_adjustments']['max_length'] = 20
                improvements['reasoning'].append("Response too short - increasing max length")
            else:
                improvements['parameter_adjustments']['max_length'] = 8
                improvements['reasoning'].append("Response too long - decreasing max length")

        return improvements

    def _get_fallback_response(self, context: str) -> str:
        """Get a fallback response for common inputs when generation fails."""
        emergency_response = self.emergency_system.get_emergency_response(context)
        return emergency_response['response']
    
    def apply_self_correction(self, original_response: str, context: str = "") -> Dict[str, Any]:
        """
        Attempt to generate a better response using self-analysis.
        """
        # Analyze the original response
        analysis = self.analyze_response_quality(original_response, context)
        
        # If quality is already good, don't change
        if analysis['quality_scores']['overall'] > 0.7:
            return {
                'improved_response': original_response,
                'analysis': analysis,
                'improvement_applied': False,
                'reason': 'Original response quality is acceptable'
            }
        
        # Get improvement suggestions
        improvements = self.suggest_generation_improvements(analysis)

        # Check if we should use a fallback response for critical issues
        if improvements['priority'] == 'critical' and improvements.get('fallback_response'):
            fallback_response = improvements['fallback_response']

            # Analyze the fallback response
            fallback_analysis = self.analyze_response_quality(fallback_response, context)

            improvement_record = {
                'timestamp': datetime.now().isoformat(),
                'original_response': original_response,
                'improved_response': fallback_response,
                'original_quality': analysis['quality_scores']['overall'],
                'improved_quality': fallback_analysis['quality_scores']['overall'],
                'method': 'fallback_response',
                'reasoning': improvements['reasoning'] + ['Used fallback response due to critical quality issues']
            }

            self.improvement_attempts.append(improvement_record)

            return {
                'improved_response': fallback_response,
                'original_analysis': analysis,
                'improved_analysis': fallback_analysis,
                'improvement_applied': True,
                'improvement_record': improvement_record,
                'quality_improvement': fallback_analysis['quality_scores']['overall'] - analysis['quality_scores']['overall'],
                'method': 'fallback'
            }

        # Try to generate a better response
        try:
            # Prepare input
            input_text = f"<human>{context}<eos><holly>"
            input_ids = torch.tensor([self.tokenizer.encode(input_text)])

            # Apply suggested parameters
            gen_params = {
                'max_length': improvements['parameter_adjustments'].get('max_length', 20),
                'temperature': improvements['parameter_adjustments'].get('temperature', 0.7),
                'top_k': improvements['parameter_adjustments'].get('top_k', 50),
                'top_p': improvements['parameter_adjustments'].get('top_p', 0.9),
                'do_sample': improvements['parameter_adjustments'].get('do_sample', True)
            }

            # Generate improved response
            with torch.no_grad():
                result = self.model.generate(input_ids, **gen_params)

            # Decode new response
            new_tokens = result['new_tokens'][0].tolist()
            improved_response = self.tokenizer.decode(new_tokens, skip_special_tokens=True)
            improved_response = improved_response.replace("<eos>", "").strip()

            # If the improved response is still very poor, use fallback
            improved_analysis = self.analyze_response_quality(improved_response, context)
            if improved_analysis['quality_scores']['overall'] < 0.3 and improvements.get('fallback_response'):
                improved_response = improvements['fallback_response']
                improved_analysis = self.analyze_response_quality(improved_response, context)
                method = 'generation_then_fallback'
            else:
                method = 'regeneration'

            # Record the improvement attempt
            improvement_record = {
                'timestamp': datetime.now().isoformat(),
                'original_response': original_response,
                'improved_response': improved_response,
                'original_quality': analysis['quality_scores']['overall'],
                'improved_quality': improved_analysis['quality_scores']['overall'],
                'parameters_used': gen_params,
                'reasoning': improvements['reasoning'],
                'method': method
            }

            self.improvement_attempts.append(improvement_record)

            return {
                'improved_response': improved_response,
                'original_analysis': analysis,
                'improved_analysis': improved_analysis,
                'improvement_applied': True,
                'improvement_record': improvement_record,
                'quality_improvement': improved_analysis['quality_scores']['overall'] - analysis['quality_scores']['overall'],
                'method': method
            }
            
        except Exception as e:
            return {
                'improved_response': original_response,
                'analysis': analysis,
                'improvement_applied': False,
                'error': str(e),
                'reason': 'Failed to generate improved response'
            }
    
    def get_improvement_history(self) -> Dict[str, Any]:
        """Get history of self-improvement attempts."""
        if not self.improvement_attempts:
            return {'status': 'no_improvements_attempted'}
        
        recent_attempts = self.improvement_attempts[-10:]
        
        # Calculate improvement statistics
        successful_improvements = [
            attempt for attempt in recent_attempts
            if attempt['improved_quality'] > attempt['original_quality']
        ]
        
        return {
            'total_attempts': len(self.improvement_attempts),
            'recent_attempts': len(recent_attempts),
            'successful_improvements': len(successful_improvements),
            'success_rate': len(successful_improvements) / len(recent_attempts) if recent_attempts else 0,
            'average_quality_gain': sum(
                attempt['improved_quality'] - attempt['original_quality']
                for attempt in successful_improvements
            ) / len(successful_improvements) if successful_improvements else 0,
            'recent_improvement_attempts': recent_attempts[-5:]
        }
