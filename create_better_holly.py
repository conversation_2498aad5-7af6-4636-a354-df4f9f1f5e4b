#!/usr/bin/env python3
"""
Create a better-initialized Holly model.

This script creates a larger, better-configured Holly model
with improved initialization for more coherent language generation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from holly.core.model import HollyModel
from holly.core.tokenizer import HollyTokenizer
from holly.learning.trainer import CollaborativeTrainer
import os
import json

def create_better_tokenizer():
    """Create a tokenizer with better vocabulary for English."""
    print("📝 Creating improved tokenizer...")
    
    tokenizer = HollyTokenizer(vocab_size=16384, min_frequency=1)
    
    # Add common English words to help with coherent generation
    common_words = [
        # Basic words
        "hello", "hi", "hey", "goodbye", "bye", "thanks", "thank", "you", "please",
        "yes", "no", "maybe", "sure", "okay", "ok", "good", "bad", "great", "nice",
        
        # Pronouns and basic grammar
        "i", "you", "he", "she", "it", "we", "they", "me", "him", "her", "us", "them",
        "my", "your", "his", "her", "its", "our", "their", "mine", "yours",
        "am", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had",
        "do", "does", "did", "will", "would", "could", "should", "can", "may", "might",
        
        # Common verbs
        "go", "come", "see", "look", "get", "give", "take", "make", "know", "think",
        "say", "tell", "ask", "help", "want", "need", "like", "love", "hate", "feel",
        "work", "play", "run", "walk", "talk", "speak", "listen", "hear", "read", "write",
        
        # Common nouns
        "time", "day", "year", "way", "man", "woman", "child", "person", "people",
        "thing", "place", "home", "work", "school", "life", "world", "country",
        "name", "number", "part", "question", "answer", "problem", "solution",
        
        # AI-specific terms
        "ai", "artificial", "intelligence", "model", "learn", "learning", "train",
        "training", "data", "information", "knowledge", "understand", "response",
        "conversation", "chat", "talk", "communicate", "language", "english",
        "holly", "assistant", "help", "helpful", "sorry", "apologize", "mistake",
        "error", "correct", "wrong", "right", "improve", "better", "best",
        
        # Common adjectives
        "big", "small", "large", "little", "long", "short", "high", "low", "new", "old",
        "young", "fast", "slow", "hot", "cold", "warm", "cool", "easy", "hard", "simple",
        "difficult", "important", "interesting", "beautiful", "ugly", "happy", "sad",
        
        # Numbers and basic concepts
        "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten",
        "first", "second", "third", "last", "next", "before", "after", "now", "then",
        "here", "there", "where", "when", "what", "who", "why", "how", "which",
    ]
    
    # Learn these words by creating simple sentences
    for word in common_words:
        tokenizer.learn_from_text(word, update_vocab=True)
        # Also learn with basic context
        tokenizer.learn_from_text(f"The word {word} is common.", update_vocab=True)
    
    print(f"✅ Improved tokenizer created with {tokenizer.get_vocab_size()} tokens")
    return tokenizer

def create_better_model(tokenizer):
    """Create a larger, better-initialized Holly model."""
    print("🧠 Creating improved Holly model...")
    
    # Larger model configuration
    config = {
        'vocab_size': tokenizer.get_vocab_size(),
        'd_model': 512,  # Increased from 256
        'n_layers': 8,   # Increased from 4
        'n_heads': 8,
        'd_ff': 2048,    # Increased feed-forward size
        'max_seq_length': 1024,
        'dropout': 0.1,
        'activation': 'gelu',
        'use_gating': False,
        'adaptive_norm': True,
        'reasoning_mode': True
    }
    
    model = HollyModel(**config)
    
    # Better weight initialization
    print("🎯 Applying improved weight initialization...")
    with torch.no_grad():
        for name, param in model.named_parameters():
            if 'weight' in name:
                if 'embedding' in name:
                    # Smaller embeddings for better stability
                    nn.init.normal_(param, mean=0.0, std=0.02)
                elif 'output_projection' in name:
                    # Tie output projection to embeddings for better language modeling
                    if hasattr(model.embeddings, 'token_embeddings'):
                        param.copy_(model.embeddings.token_embeddings.weight)
                elif len(param.shape) >= 2:
                    # Xavier initialization for linear layers
                    nn.init.xavier_uniform_(param, gain=0.02)
                else:
                    # Small initialization for biases and 1D parameters
                    nn.init.normal_(param, mean=0.0, std=0.01)
            elif 'bias' in name:
                nn.init.zeros_(param)
    
    print(f"✅ Improved model created with {sum(p.numel() for p in model.parameters()):,} parameters")
    return model

def train_basic_patterns(model, tokenizer):
    """Train the model on very basic language patterns."""
    print("📚 Training on basic language patterns...")
    
    trainer = CollaborativeTrainer(model, tokenizer, learning_rate=5e-4)
    session_id = trainer.start_learning_session("improved_basic_training")
    
    # Very simple training examples focusing on basic patterns
    simple_examples = [
        # Single word responses
        "Hello. Hi.",
        "Hi. Hello.",
        "Thanks. Welcome.",
        "Yes. Good.",
        "No. Sorry.",
        
        # Two word responses
        "Hello there. Hi there.",
        "Thank you. You welcome.",
        "How are. I good.",
        "What name. My Holly.",
        "Who you. I Holly.",
        
        # Simple sentences
        "Hello. Hello, how are you?",
        "Hi. Hi, I am Holly.",
        "Thanks. You are welcome.",
        "Help me. I will help.",
        "What is. It is good.",
        
        # Basic conversation
        "Hi Holly. Hello! How can I help?",
        "How are you. I am doing well, thank you.",
        "What is your name. My name is Holly.",
        "Can you help. Yes, I can help you.",
        "Thank you Holly. You are very welcome!",
    ]
    
    # Train multiple times with very small steps
    for epoch in range(3):
        print(f"  Epoch {epoch + 1}/3")
        total_loss = 0
        
        for example in simple_examples:
            parts = example.split(". ")
            if len(parts) == 2:
                human_msg, holly_msg = parts
                
                conversation = [
                    {"role": "human", "content": human_msg},
                    {"role": "holly", "content": holly_msg}
                ]
                
                result = trainer.learn_from_conversation(
                    conversation,
                    session_id=session_id,
                    apply_immediately=True
                )
                total_loss += result['loss']
        
        avg_loss = total_loss / len(simple_examples)
        print(f"    Average loss: {avg_loss:.4f}")
    
    print("✅ Basic pattern training completed")

def test_model_generation(model, tokenizer):
    """Test the model's generation capabilities."""
    print("🧪 Testing model generation...")
    
    model.eval()
    test_inputs = ["Hello", "Hi", "What is your name", "How are you", "Thank you"]
    
    for test_input in test_inputs:
        print(f"\n👤 Input: {test_input}")
        
        # Prepare input with proper formatting
        input_text = f"<human>{test_input}<eos><holly>"
        input_ids = torch.tensor([tokenizer.encode(input_text)])
        
        # Generate with conservative settings
        with torch.no_grad():
            result = model.generate(
                input_ids,
                max_length=20,
                temperature=0.3,  # Lower temperature for more focused generation
                top_k=10,         # Limit to top 10 tokens
                top_p=0.8,        # Nucleus sampling
                do_sample=True
            )
        
        # Decode response
        new_tokens = result['new_tokens'][0].tolist()
        response = tokenizer.decode(new_tokens, skip_special_tokens=True)
        response = response.replace("<eos>", "").strip()
        
        print(f"🤖 Holly: {response}")

def main():
    """Create and save the improved Holly model."""
    print("🌟 Creating Better Holly Model")
    print("=" * 40)
    
    # Create improved components
    tokenizer = create_better_tokenizer()
    model = create_better_model(tokenizer)
    
    # Train on basic patterns
    train_basic_patterns(model, tokenizer)
    
    # Test generation
    test_model_generation(model, tokenizer)
    
    # Save the improved model and tokenizer
    model_path = "models/holly_better.pt"
    tokenizer_path = "models/holly_better_tokenizer.json"
    
    model.save_model(model_path)
    tokenizer.save_vocabulary(tokenizer_path)
    
    print(f"\n✅ Improved Holly saved:")
    print(f"   Model: {model_path}")
    print(f"   Tokenizer: {tokenizer_path}")
    print(f"   Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    print(f"\n🚀 To use the improved Holly:")
    print(f"   python holly/server.py --model-path {model_path}")
    
    return model_path, tokenizer_path

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Error creating better Holly: {e}")
        import traceback
        traceback.print_exc()
