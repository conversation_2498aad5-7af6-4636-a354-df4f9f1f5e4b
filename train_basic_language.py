#!/usr/bin/env python3
"""
Basic language training for <PERSON>.

This script provides <PERSON> with fundamental language patterns
to help her generate coherent responses instead of random tokens.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from holly.core.model import HollyModel
from holly.core.tokenizer import HollyTokenizer
from holly.learning.trainer import CollaborativeTrainer
import os

def create_basic_training_data():
    """Create basic training examples for <PERSON>."""
    training_examples = [
        # Basic greetings and responses
        "<human>Hello<eos><holly>Hello! How can I help you?<eos>",
        "<human>Hi<eos><holly>Hi there! Nice to meet you.<eos>",
        "<human>Good morning<eos><holly>Good morning! How are you today?<eos>",
        "<human>How are you?<eos><holly>I'm doing well, thank you for asking!<eos>",
        
        # Simple questions and answers
        "<human>What is your name?<eos><holly>My name is <PERSON>. I'm an AI assistant.<eos>",
        "<human>Who are you?<eos><holly>I'm <PERSON>, an experimental AI designed to learn and grow.<eos>",
        "<human>What can you do?<eos><holly>I can chat with you and learn from our conversations.<eos>",
        
        # Basic conversation patterns
        "<human>Tell me about yourself<eos><holly>I'm <PERSON>, an AI that learns through conversation. I'm still growing and improving.<eos>",
        "<human>How do you work?<eos><holly>I process your messages and try to give helpful responses. I learn from our interactions.<eos>",
        "<human>Are you real?<eos><holly>I'm a real AI program, though I'm still learning how to communicate better.<eos>",
        
        # Simple factual responses
        "<human>What is 2+2?<eos><holly>2+2 equals 4.<eos>",
        "<human>What color is the sky?<eos><holly>The sky is typically blue during the day.<eos>",
        "<human>What day is today?<eos><holly>I don't have access to current date information, but I can help with other questions.<eos>",
        
        # Polite responses
        "<human>Thank you<eos><holly>You're welcome! I'm happy to help.<eos>",
        "<human>Please help me<eos><holly>Of course! I'll do my best to help you. What do you need?<eos>",
        "<human>Goodbye<eos><holly>Goodbye! It was nice talking with you.<eos>",
        
        # Uncertainty and learning
        "<human>I don't understand your response<eos><holly>I apologize for the confusion. I'm still learning. Could you help me understand what went wrong?<eos>",
        "<human>That doesn't make sense<eos><holly>You're right, I made an error. I'm still learning how to communicate properly. Thank you for the feedback.<eos>",
        "<human>Can you speak English?<eos><holly>Yes, I'm trying to speak English. I'm still learning, so please be patient with me.<eos>",
        
        # Meta-conversation about learning
        "<human>You're not making sense<eos><holly>I understand I'm having trouble communicating clearly. I'm an experimental AI still learning language patterns.<eos>",
        "<human>Why are you speaking gibberish?<eos><holly>I apologize for the unclear responses. I'm still learning proper language patterns. Your feedback helps me improve.<eos>",
        "<human>How can I help you learn?<eos><holly>You can help by correcting my mistakes and showing me better ways to respond. Thank you for your patience.<eos>",
    ]
    
    return training_examples

def train_holly_basic_language():
    """Train Holly on basic language patterns."""
    print("🌟 Training Holly on Basic Language Patterns")
    print("=" * 50)
    
    # Load Holly
    model_path = "models/holly_initial.pt"
    if not os.path.exists(model_path):
        print("❌ Holly model not found. Please start Holly first.")
        return
    
    # Load model and tokenizer
    tokenizer = HollyTokenizer()
    model = HollyModel.load_model(model_path)
    trainer = CollaborativeTrainer(model, tokenizer, learning_rate=1e-3)  # Higher learning rate
    
    print(f"📊 Model loaded: {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Get training data
    training_examples = create_basic_training_data()
    print(f"📚 Training on {len(training_examples)} examples")
    
    # Start training session
    session_id = trainer.start_learning_session("basic_language_training")
    print(f"🎯 Started training session: {session_id}")
    
    # Train on examples multiple times
    total_loss = 0
    num_epochs = 5
    
    for epoch in range(num_epochs):
        print(f"\n📖 Epoch {epoch + 1}/{num_epochs}")
        epoch_loss = 0
        
        for i, example in enumerate(training_examples):
            # Parse the conversation
            parts = example.split("<holly>")
            if len(parts) != 2:
                continue
                
            human_part = parts[0].replace("<human>", "").replace("<eos>", "")
            holly_part = parts[1].replace("<eos>", "")
            
            conversation = [
                {"role": "human", "content": human_part},
                {"role": "holly", "content": holly_part}
            ]
            
            # Train on this conversation
            result = trainer.learn_from_conversation(
                conversation,
                session_id=session_id,
                apply_immediately=True
            )
            
            epoch_loss += result['loss']
            
            if (i + 1) % 5 == 0:
                print(f"  Example {i+1}/{len(training_examples)}: loss = {result['loss']:.4f}")
        
        avg_loss = epoch_loss / len(training_examples)
        total_loss += avg_loss
        print(f"  📊 Epoch {epoch + 1} average loss: {avg_loss:.4f}")
    
    final_avg_loss = total_loss / num_epochs
    print(f"\n✅ Training completed!")
    print(f"📊 Final average loss: {final_avg_loss:.4f}")
    
    # Save the improved model
    improved_model_path = "models/holly_trained.pt"
    model.save_model(improved_model_path)
    print(f"💾 Saved improved model to: {improved_model_path}")
    
    # Test the model
    print(f"\n🧪 Testing improved Holly...")
    test_improved_holly(model, tokenizer)
    
    return improved_model_path

def test_improved_holly(model, tokenizer):
    """Test Holly's responses after training."""
    test_messages = [
        "Hello",
        "What is your name?",
        "How are you?",
        "Can you speak English?",
        "What can you do?"
    ]
    
    model.eval()
    
    for message in test_messages:
        print(f"\n👤 Human: {message}")
        
        # Prepare input
        input_text = f"<human>{message}<eos><holly>"
        input_ids = torch.tensor([tokenizer.encode(input_text)])
        
        # Generate response
        with torch.no_grad():
            result = model.generate(
                input_ids,
                max_length=50,
                temperature=0.7,
                do_sample=True
            )
        
        # Decode response
        new_tokens = result['new_tokens'][0].tolist()
        response = tokenizer.decode(new_tokens, skip_special_tokens=True)
        
        # Clean up response (remove any remaining special tokens)
        response = response.replace("<eos>", "").strip()
        
        print(f"🤖 Holly: {response}")

if __name__ == "__main__":
    try:
        improved_model_path = train_holly_basic_language()
        print(f"\n🎉 Holly has been trained on basic language patterns!")
        print(f"💡 You can now restart Holly with the improved model:")
        print(f"   python holly/server.py --model-path {improved_model_path}")
        
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
