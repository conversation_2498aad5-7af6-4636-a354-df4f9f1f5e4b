"""
WebSocket handler for <PERSON>'s real-time communication.

This module manages WebSocket connections for live chat
and real-time updates from <PERSON>'s learning system.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging


class WebSocketHandler:
    """
    Handles WebSocket connections for real-time communication with <PERSON>.
    
    Features:
    - Real-time chat messaging
    - Learning progress updates
    - Model insights broadcasting
    - Connection management
    """
    
    def __init__(self):
        self.active_connections: List[Any] = []  # WebSocket connections
        self.connection_metadata: Dict[str, Dict] = {}
        self.message_queue: List[Dict] = []
        self.logger = logging.getLogger('holly.websocket')
    
    async def connect(self, websocket, client_id: Optional[str] = None):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # Generate client ID if not provided
        if client_id is None:
            client_id = f"client_{len(self.active_connections)}_{datetime.now().strftime('%H%M%S')}"
        
        # Store connection metadata
        self.connection_metadata[client_id] = {
            'websocket': websocket,
            'connected_at': datetime.now().isoformat(),
            'message_count': 0,
            'last_activity': datetime.now().isoformat()
        }
        
        # Send welcome message
        await self.send_to_client(websocket, {
            'type': 'connection_established',
            'client_id': client_id,
            'timestamp': datetime.now().isoformat(),
            'message': 'Connected to Holly WebSocket'
        })
        
        self.logger.info(f"New WebSocket connection: {client_id}")
        return client_id
    
    def disconnect(self, websocket):
        """Handle WebSocket disconnection."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # Remove from metadata
        client_id = None
        for cid, metadata in self.connection_metadata.items():
            if metadata['websocket'] == websocket:
                client_id = cid
                break
        
        if client_id:
            del self.connection_metadata[client_id]
            self.logger.info(f"WebSocket disconnected: {client_id}")
    
    async def send_to_client(self, websocket, message: Dict[str, Any]):
        """Send message to a specific client."""
        try:
            message_json = json.dumps(message)
            await websocket.send_text(message_json)
        except Exception as e:
            self.logger.error(f"Error sending message to client: {e}")
            # Remove disconnected client
            self.disconnect(websocket)
    
    async def broadcast_message(self, message: Dict[str, Any], exclude_client: Optional[Any] = None):
        """Broadcast message to all connected clients."""
        if not self.active_connections:
            return
        
        message_json = json.dumps(message)
        disconnected_clients = []
        
        for websocket in self.active_connections:
            if websocket == exclude_client:
                continue
            
            try:
                await websocket.send_text(message_json)
            except Exception as e:
                self.logger.error(f"Error broadcasting to client: {e}")
                disconnected_clients.append(websocket)
        
        # Clean up disconnected clients
        for websocket in disconnected_clients:
            self.disconnect(websocket)
    
    async def handle_client_message(self, websocket, message_data: str):
        """Handle incoming message from client."""
        try:
            message = json.loads(message_data)
            message_type = message.get('type', 'unknown')
            
            # Update client activity
            self._update_client_activity(websocket)
            
            # Handle different message types
            if message_type == 'ping':
                await self.send_to_client(websocket, {
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat()
                })
            
            elif message_type == 'chat_message':
                await self._handle_chat_message(websocket, message)
            
            elif message_type == 'request_insights':
                await self._handle_insights_request(websocket, message)
            
            elif message_type == 'feedback':
                await self._handle_feedback(websocket, message)
            
            else:
                await self.send_to_client(websocket, {
                    'type': 'error',
                    'message': f'Unknown message type: {message_type}',
                    'timestamp': datetime.now().isoformat()
                })
        
        except json.JSONDecodeError:
            await self.send_to_client(websocket, {
                'type': 'error',
                'message': 'Invalid JSON format',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"Error handling client message: {e}")
            await self.send_to_client(websocket, {
                'type': 'error',
                'message': 'Internal server error',
                'timestamp': datetime.now().isoformat()
            })
    
    def _update_client_activity(self, websocket):
        """Update last activity timestamp for client."""
        for metadata in self.connection_metadata.values():
            if metadata['websocket'] == websocket:
                metadata['last_activity'] = datetime.now().isoformat()
                metadata['message_count'] += 1
                break
    
    async def _handle_chat_message(self, websocket, message: Dict[str, Any]):
        """Handle chat message from client."""
        # This would integrate with the chat interface
        # For now, just echo back
        response = {
            'type': 'chat_response',
            'original_message': message.get('content', ''),
            'response': 'Message received (WebSocket handler)',
            'timestamp': datetime.now().isoformat()
        }
        
        await self.send_to_client(websocket, response)
    
    async def _handle_insights_request(self, websocket, message: Dict[str, Any]):
        """Handle request for model insights."""
        # This would integrate with Holly's insights system
        insights = {
            'type': 'insights_response',
            'insights': {
                'connections': len(self.active_connections),
                'total_messages': sum(m['message_count'] for m in self.connection_metadata.values()),
                'uptime': 'WebSocket handler active'
            },
            'timestamp': datetime.now().isoformat()
        }
        
        await self.send_to_client(websocket, insights)
    
    async def _handle_feedback(self, websocket, message: Dict[str, Any]):
        """Handle feedback from client."""
        feedback_response = {
            'type': 'feedback_received',
            'feedback_id': message.get('feedback_id', 'unknown'),
            'status': 'received',
            'timestamp': datetime.now().isoformat()
        }
        
        await self.send_to_client(websocket, feedback_response)
    
    async def broadcast_learning_update(self, learning_data: Dict[str, Any]):
        """Broadcast learning progress update to all clients."""
        message = {
            'type': 'learning_update',
            'data': learning_data,
            'timestamp': datetime.now().isoformat()
        }
        
        await self.broadcast_message(message)
    
    async def broadcast_model_insights(self, insights: Dict[str, Any]):
        """Broadcast model insights to all clients."""
        message = {
            'type': 'model_insights',
            'insights': insights,
            'timestamp': datetime.now().isoformat()
        }
        
        await self.broadcast_message(message)
    
    async def broadcast_system_status(self, status: Dict[str, Any]):
        """Broadcast system status update."""
        message = {
            'type': 'system_status',
            'status': status,
            'timestamp': datetime.now().isoformat()
        }
        
        await self.broadcast_message(message)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get statistics about WebSocket connections."""
        return {
            'active_connections': len(self.active_connections),
            'total_messages': sum(m['message_count'] for m in self.connection_metadata.values()),
            'connection_details': [
                {
                    'client_id': cid,
                    'connected_at': metadata['connected_at'],
                    'message_count': metadata['message_count'],
                    'last_activity': metadata['last_activity']
                }
                for cid, metadata in self.connection_metadata.items()
            ]
        }
    
    async def cleanup_inactive_connections(self, timeout_minutes: int = 30):
        """Clean up inactive connections."""
        current_time = datetime.now()
        inactive_connections = []
        
        for client_id, metadata in self.connection_metadata.items():
            last_activity = datetime.fromisoformat(metadata['last_activity'])
            time_diff = (current_time - last_activity).total_seconds() / 60
            
            if time_diff > timeout_minutes:
                inactive_connections.append(metadata['websocket'])
        
        for websocket in inactive_connections:
            await self.send_to_client(websocket, {
                'type': 'connection_timeout',
                'message': 'Connection closed due to inactivity',
                'timestamp': datetime.now().isoformat()
            })
            self.disconnect(websocket)
        
        if inactive_connections:
            self.logger.info(f"Cleaned up {len(inactive_connections)} inactive connections")
