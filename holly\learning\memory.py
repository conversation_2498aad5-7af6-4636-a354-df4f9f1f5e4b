"""
Memory systems for <PERSON>.

This module implements episodic and conversation memory
to help <PERSON> retain and build upon previous interactions.
"""

import torch
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from collections import defaultdict
import sqlite3
import pickle


class EpisodicMemory:
    """
    Episodic memory system for Holly.
    
    Stores and retrieves specific conversation episodes
    and learning experiences for future reference.
    """
    
    def __init__(self, db_path: str = "data/holly_memory.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize the memory database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create episodes table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS episodes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                episode_type TEXT NOT NULL,
                content TEXT NOT NULL,
                metadata TEXT,
                embedding BLOB,
                importance_score REAL DEFAULT 0.5
            )
        """)
        
        # Create associations table for linking related episodes
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS associations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                episode1_id INTEGER,
                episode2_id INTEGER,
                association_type TEXT,
                strength REAL,
                FOREIGN KEY (episode1_id) REFERENCES episodes (id),
                FOREIGN KEY (episode2_id) REFERENCES episodes (id)
            )
        """)
        
        conn.commit()
        conn.close()
    
    def store_episode(
        self,
        episode_type: str,
        content: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        importance_score: float = 0.5
    ) -> int:
        """
        Store a new episode in memory.
        
        Args:
            episode_type: Type of episode (conversation, learning, feedback, etc.)
            content: Episode content
            metadata: Additional metadata
            importance_score: Importance score (0-1)
            
        Returns:
            Episode ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        timestamp = datetime.now().isoformat()
        content_json = json.dumps(content)
        metadata_json = json.dumps(metadata) if metadata else None
        
        cursor.execute("""
            INSERT INTO episodes (timestamp, episode_type, content, metadata, importance_score)
            VALUES (?, ?, ?, ?, ?)
        """, (timestamp, episode_type, content_json, metadata_json, importance_score))
        
        episode_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return episode_id
    
    def retrieve_episodes(
        self,
        episode_type: Optional[str] = None,
        limit: int = 10,
        min_importance: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        Retrieve episodes from memory.
        
        Args:
            episode_type: Filter by episode type
            limit: Maximum number of episodes to return
            min_importance: Minimum importance score
            
        Returns:
            List of episodes
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = """
            SELECT id, timestamp, episode_type, content, metadata, importance_score
            FROM episodes
            WHERE importance_score >= ?
        """
        params = [min_importance]
        
        if episode_type:
            query += " AND episode_type = ?"
            params.append(episode_type)
        
        query += " ORDER BY importance_score DESC, timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        conn.close()
        
        episodes = []
        for row in rows:
            episode = {
                'id': row[0],
                'timestamp': row[1],
                'episode_type': row[2],
                'content': json.loads(row[3]),
                'metadata': json.loads(row[4]) if row[4] else None,
                'importance_score': row[5]
            }
            episodes.append(episode)
        
        return episodes
    
    def create_association(
        self,
        episode1_id: int,
        episode2_id: int,
        association_type: str,
        strength: float = 1.0
    ):
        """Create an association between two episodes."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO associations (episode1_id, episode2_id, association_type, strength)
            VALUES (?, ?, ?, ?)
        """, (episode1_id, episode2_id, association_type, strength))
        
        conn.commit()
        conn.close()
    
    def get_related_episodes(self, episode_id: int) -> List[Dict[str, Any]]:
        """Get episodes related to a given episode."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT e.id, e.timestamp, e.episode_type, e.content, e.metadata, 
                   e.importance_score, a.association_type, a.strength
            FROM episodes e
            JOIN associations a ON (e.id = a.episode2_id AND a.episode1_id = ?)
                                OR (e.id = a.episode1_id AND a.episode2_id = ?)
            ORDER BY a.strength DESC, e.importance_score DESC
        """, (episode_id, episode_id))
        
        rows = cursor.fetchall()
        conn.close()
        
        related = []
        for row in rows:
            episode = {
                'id': row[0],
                'timestamp': row[1],
                'episode_type': row[2],
                'content': json.loads(row[3]),
                'metadata': json.loads(row[4]) if row[4] else None,
                'importance_score': row[5],
                'association_type': row[6],
                'association_strength': row[7]
            }
            related.append(episode)
        
        return related


class ConversationMemory:
    """
    Short-term conversation memory for maintaining context
    within and across conversation sessions.
    """
    
    def __init__(self, max_context_length: int = 2048):
        self.max_context_length = max_context_length
        self.current_conversation = []
        self.conversation_history = []
        self.context_embeddings = []
        
    def add_message(
        self,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Add a message to current conversation."""
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        self.current_conversation.append(message)
        
        # Trim conversation if too long
        self._trim_conversation()
    
    def _trim_conversation(self):
        """Trim conversation to stay within context limits."""
        # Simple token counting approximation
        total_tokens = sum(len(msg['content'].split()) for msg in self.current_conversation)
        
        while total_tokens > self.max_context_length and len(self.current_conversation) > 1:
            # Remove oldest message (but keep at least one)
            removed = self.current_conversation.pop(0)
            total_tokens -= len(removed['content'].split())
    
    def get_context(self, include_metadata: bool = False) -> List[Dict[str, Any]]:
        """Get current conversation context."""
        if include_metadata:
            return self.current_conversation.copy()
        else:
            return [
                {'role': msg['role'], 'content': msg['content']}
                for msg in self.current_conversation
            ]
    
    def end_conversation(self) -> Dict[str, Any]:
        """End current conversation and archive it."""
        if not self.current_conversation:
            return {}
        
        conversation_summary = {
            'messages': self.current_conversation.copy(),
            'start_time': self.current_conversation[0]['timestamp'],
            'end_time': datetime.now().isoformat(),
            'message_count': len(self.current_conversation),
            'total_tokens': sum(len(msg['content'].split()) for msg in self.current_conversation)
        }
        
        self.conversation_history.append(conversation_summary)
        self.current_conversation = []
        
        return conversation_summary
    
    def get_recent_conversations(self, count: int = 5) -> List[Dict[str, Any]]:
        """Get recent conversation summaries."""
        return self.conversation_history[-count:]
    
    def search_conversations(self, query: str) -> List[Dict[str, Any]]:
        """Search through conversation history."""
        query_lower = query.lower()
        matching_conversations = []
        
        for conv in self.conversation_history:
            # Simple text search
            for message in conv['messages']:
                if query_lower in message['content'].lower():
                    matching_conversations.append(conv)
                    break
        
        return matching_conversations
