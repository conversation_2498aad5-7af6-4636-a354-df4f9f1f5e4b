"""
Hybrid Intelligence System for Holly.

This system allows <PERSON> to dynamically use different pre-trained models
and combine their capabilities for maximum intelligence.
"""

import torch
import torch.nn as nn
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoModel,
    pipeline, GPT2LMHeadModel, GPT2Tokenizer
)
import asyncio
import numpy as np
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime
import json

class ModelEnsemble:
    """Ensemble of different pre-trained models for maximum capability."""
    
    def __init__(self):
        self.models = {}
        self.tokenizers = {}
        self.model_capabilities = {}
        self.load_models()
        
    def load_models(self):
        """Load multiple pre-trained models for different capabilities."""
        model_configs = [
            {
                'name': 'conversation',
                'model_id': 'microsoft/DialoGPT-medium',
                'capability': 'dialogue and conversation',
                'priority': 0.9
            },
            {
                'name': 'reasoning',
                'model_id': 'gpt2-medium',
                'capability': 'general reasoning and text generation',
                'priority': 0.8
            },
            {
                'name': 'analysis',
                'model_id': 'distilbert-base-uncased',
                'capability': 'text analysis and understanding',
                'priority': 0.7
            }
        ]
        
        for config in model_configs:
            try:
                print(f"Loading {config['name']} model: {config['model_id']}")
                
                if 'bert' in config['model_id'].lower():
                    # For BERT-like models (analysis)
                    tokenizer = AutoTokenizer.from_pretrained(config['model_id'])
                    model = AutoModel.from_pretrained(config['model_id'])
                else:
                    # For GPT-like models (generation)
                    tokenizer = AutoTokenizer.from_pretrained(config['model_id'])
                    model = AutoModelForCausalLM.from_pretrained(config['model_id'])
                    
                    if tokenizer.pad_token is None:
                        tokenizer.pad_token = tokenizer.eos_token
                
                self.models[config['name']] = model
                self.tokenizers[config['name']] = tokenizer
                self.model_capabilities[config['name']] = config
                
                print(f"✅ Loaded {config['name']} model successfully")
                
            except Exception as e:
                print(f"❌ Failed to load {config['name']} model: {e}")
                continue
    
    def select_best_model(self, task_type: str, context: str = "") -> str:
        """Select the best model for a given task."""
        task_preferences = {
            'conversation': ['conversation', 'reasoning'],
            'analysis': ['analysis', 'reasoning'],
            'reasoning': ['reasoning', 'conversation'],
            'creative': ['reasoning', 'conversation'],
            'factual': ['reasoning', 'analysis']
        }
        
        preferred_models = task_preferences.get(task_type, ['conversation'])
        
        # Return the first available preferred model
        for model_name in preferred_models:
            if model_name in self.models:
                return model_name
        
        # Fallback to any available model
        return list(self.models.keys())[0] if self.models else None
    
    def generate_with_model(self, model_name: str, input_text: str, **kwargs) -> str:
        """Generate text using a specific model."""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not available")
        
        model = self.models[model_name]
        tokenizer = self.tokenizers[model_name]
        
        # Check if model supports generation
        if not hasattr(model, 'generate'):
            return f"[Model {model_name} does not support text generation]"
        
        try:
            inputs = tokenizer.encode(input_text, return_tensors='pt')
            
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=inputs.size(1) + kwargs.get('max_length', 100),
                    temperature=kwargs.get('temperature', 0.8),
                    top_k=kwargs.get('top_k', 50),
                    top_p=kwargs.get('top_p', 0.9),
                    do_sample=kwargs.get('do_sample', True),
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    no_repeat_ngram_size=3
                )
            
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the new part
            if input_text in generated_text:
                response = generated_text[len(input_text):].strip()
            else:
                response = generated_text.strip()
            
            return response
            
        except Exception as e:
            logging.error(f"Generation failed with {model_name}: {e}")
            return f"[Generation error with {model_name}]"
    
    def ensemble_generate(self, input_text: str, task_type: str = 'conversation', **kwargs) -> Dict[str, Any]:
        """Generate using ensemble of models and select best response."""
        responses = {}
        
        # Get responses from multiple models
        for model_name in self.models:
            if hasattr(self.models[model_name], 'generate'):
                try:
                    response = self.generate_with_model(model_name, input_text, **kwargs)
                    responses[model_name] = {
                        'text': response,
                        'model_capability': self.model_capabilities[model_name]['capability'],
                        'priority': self.model_capabilities[model_name]['priority']
                    }
                except Exception as e:
                    logging.error(f"Ensemble generation failed for {model_name}: {e}")
        
        if not responses:
            return {'text': "I'm having trouble generating a response.", 'method': 'fallback'}
        
        # Select best response based on task type and quality
        best_model = self.select_best_model(task_type)
        
        if best_model in responses:
            best_response = responses[best_model]
            best_response['method'] = f'ensemble_{best_model}'
            best_response['alternatives'] = {k: v['text'] for k, v in responses.items() if k != best_model}
            return best_response
        else:
            # Fallback to highest priority available response
            best_response = max(responses.values(), key=lambda x: x['priority'])
            best_response['method'] = 'ensemble_fallback'
            return best_response

class AdaptiveIntelligence:
    """Adaptive intelligence system that learns and improves Holly's capabilities."""
    
    def __init__(self, model_ensemble: ModelEnsemble):
        self.ensemble = model_ensemble
        self.performance_history = []
        self.adaptation_strategies = {}
        self.intelligence_metrics = {
            'response_quality': 0.5,
            'reasoning_ability': 0.5,
            'learning_speed': 0.5,
            'creativity': 0.5,
            'problem_solving': 0.5
        }
        
    def assess_response_quality(self, response: str, context: str = "") -> float:
        """Assess the quality of a generated response."""
        quality_factors = {
            'length_appropriate': self._assess_length(response, context),
            'coherence': self._assess_coherence(response),
            'relevance': self._assess_relevance(response, context),
            'creativity': self._assess_creativity(response),
            'informativeness': self._assess_informativeness(response)
        }
        
        # Weighted average
        weights = {
            'length_appropriate': 0.15,
            'coherence': 0.25,
            'relevance': 0.25,
            'creativity': 0.15,
            'informativeness': 0.20
        }
        
        quality_score = sum(quality_factors[factor] * weights[factor] 
                          for factor in quality_factors)
        
        return quality_score
    
    def _assess_length(self, response: str, context: str) -> float:
        """Assess if response length is appropriate."""
        words = len(response.split())
        
        # Context-dependent ideal length
        if any(word in context.lower() for word in ['explain', 'describe', 'tell me about']):
            ideal_range = (20, 100)
        elif any(word in context.lower() for word in ['yes', 'no', 'hello', 'hi']):
            ideal_range = (1, 20)
        else:
            ideal_range = (5, 50)
        
        min_words, max_words = ideal_range
        
        if words < min_words:
            return words / min_words
        elif words > max_words:
            return max_words / words
        else:
            return 1.0
    
    def _assess_coherence(self, response: str) -> float:
        """Assess response coherence."""
        if not response:
            return 0.0
        
        sentences = response.split('.')
        if len(sentences) < 2:
            return 0.8  # Single sentence is usually coherent
        
        # Simple coherence check
        coherence_score = 0.7  # Base score
        
        # Check for repeated words (sign of incoherence)
        words = response.lower().split()
        unique_words = set(words)
        repetition_ratio = len(words) / len(unique_words) if unique_words else 1
        
        if repetition_ratio > 2:
            coherence_score -= 0.3
        
        # Check for proper sentence structure
        proper_sentences = sum(1 for s in sentences if len(s.strip().split()) > 2)
        sentence_score = proper_sentences / len(sentences) if sentences else 0
        
        coherence_score = (coherence_score + sentence_score) / 2
        return max(0.0, min(1.0, coherence_score))
    
    def _assess_relevance(self, response: str, context: str) -> float:
        """Assess response relevance to context."""
        if not context:
            return 0.7  # Neutral if no context
        
        context_words = set(context.lower().split())
        response_words = set(response.lower().split())
        
        # Calculate word overlap
        overlap = len(context_words.intersection(response_words))
        relevance = overlap / len(context_words) if context_words else 0
        
        return min(1.0, relevance * 2)  # Scale up relevance
    
    def _assess_creativity(self, response: str) -> float:
        """Assess response creativity."""
        # Simple creativity metrics
        unique_words = len(set(response.lower().split()))
        total_words = len(response.split())
        
        if total_words == 0:
            return 0.0
        
        vocabulary_diversity = unique_words / total_words
        
        # Check for creative elements
        creative_indicators = [
            'imagine', 'perhaps', 'consider', 'interesting', 'fascinating',
            'wonder', 'explore', 'discover', 'create', 'innovative'
        ]
        
        creative_words = sum(1 for word in creative_indicators 
                           if word in response.lower())
        
        creativity_score = (vocabulary_diversity + creative_words * 0.1) / 2
        return min(1.0, creativity_score)
    
    def _assess_informativeness(self, response: str) -> float:
        """Assess how informative the response is."""
        if not response:
            return 0.0
        
        # Check for informative elements
        informative_indicators = [
            'because', 'therefore', 'however', 'although', 'specifically',
            'for example', 'such as', 'including', 'research shows',
            'studies indicate', 'evidence suggests'
        ]
        
        informative_count = sum(1 for indicator in informative_indicators
                              if indicator in response.lower())
        
        # Length factor (longer responses can be more informative)
        length_factor = min(1.0, len(response.split()) / 30)
        
        informativeness = (informative_count * 0.2 + length_factor) / 2
        return min(1.0, informativeness)
    
    def adapt_generation_strategy(self, performance_feedback: Dict[str, float]):
        """Adapt generation strategy based on performance feedback."""
        # Update intelligence metrics
        for metric, value in performance_feedback.items():
            if metric in self.intelligence_metrics:
                # Exponential moving average
                alpha = 0.3
                self.intelligence_metrics[metric] = (
                    alpha * value + (1 - alpha) * self.intelligence_metrics[metric]
                )
        
        # Record performance
        self.performance_history.append({
            'timestamp': datetime.now().isoformat(),
            'metrics': self.intelligence_metrics.copy(),
            'feedback': performance_feedback
        })
        
        # Adapt strategies based on weaknesses
        adaptations = []
        
        if self.intelligence_metrics['response_quality'] < 0.6:
            adaptations.append('increase_model_ensemble_usage')
        
        if self.intelligence_metrics['creativity'] < 0.5:
            adaptations.append('increase_temperature_for_creative_tasks')
        
        if self.intelligence_metrics['reasoning_ability'] < 0.6:
            adaptations.append('use_reasoning_focused_models')
        
        self.adaptation_strategies['current'] = adaptations
        
        return adaptations
    
    def get_intelligence_report(self) -> Dict[str, Any]:
        """Get comprehensive intelligence report."""
        return {
            'intelligence_metrics': self.intelligence_metrics,
            'available_models': list(self.ensemble.models.keys()),
            'model_capabilities': self.ensemble.model_capabilities,
            'adaptation_strategies': self.adaptation_strategies,
            'performance_trend': self._calculate_performance_trend(),
            'recommendations': self._generate_improvement_recommendations()
        }
    
    def _calculate_performance_trend(self) -> Dict[str, str]:
        """Calculate performance trends over time."""
        if len(self.performance_history) < 2:
            return {'overall': 'insufficient_data'}
        
        recent = self.performance_history[-5:]  # Last 5 records
        older = self.performance_history[-10:-5] if len(self.performance_history) >= 10 else []
        
        trends = {}
        
        for metric in self.intelligence_metrics:
            if older:
                recent_avg = sum(record['metrics'][metric] for record in recent) / len(recent)
                older_avg = sum(record['metrics'][metric] for record in older) / len(older)
                
                if recent_avg > older_avg + 0.05:
                    trends[metric] = 'improving'
                elif recent_avg < older_avg - 0.05:
                    trends[metric] = 'declining'
                else:
                    trends[metric] = 'stable'
            else:
                trends[metric] = 'new_metric'
        
        return trends
    
    def _generate_improvement_recommendations(self) -> List[str]:
        """Generate recommendations for improvement."""
        recommendations = []
        
        for metric, score in self.intelligence_metrics.items():
            if score < 0.6:
                if metric == 'response_quality':
                    recommendations.append("Consider using ensemble generation for better quality")
                elif metric == 'creativity':
                    recommendations.append("Increase temperature and explore creative prompting")
                elif metric == 'reasoning_ability':
                    recommendations.append("Focus on reasoning-specialized models")
                elif metric == 'learning_speed':
                    recommendations.append("Implement more frequent adaptation cycles")
                elif metric == 'problem_solving':
                    recommendations.append("Practice with more complex problem-solving scenarios")
        
        if not recommendations:
            recommendations.append("Performance is good across all metrics - continue current approach")
        
        return recommendations
