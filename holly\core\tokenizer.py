"""
Tokenizer for <PERSON> with adaptive vocabulary and learning capabilities.

This module implements a custom tokenizer that can grow and adapt
its vocabulary as <PERSON> encounters new words and concepts.
"""

import json
import re
from typing import List, Dict, Optional, Set, Tuple
from collections import defaultdict, Counter
import torch


class HollyTokenizer:
    """
    Adaptive tokenizer for <PERSON>.
    
    Features:
    - Byte-pair encoding (BPE) with dynamic vocabulary expansion
    - Special tokens for conversation structure
    - Adaptive learning of new tokens from conversations
    - Transparent token statistics and insights
    """
    
    def __init__(
        self, 
        vocab_size: int = 8192,
        min_frequency: int = 2,
        special_tokens: Optional[Dict[str, str]] = None
    ):
        self.vocab_size = vocab_size
        self.min_frequency = min_frequency
        
        # Special tokens for conversation and reasoning
        self.special_tokens = special_tokens or {
            '<pad>': 0,
            '<unk>': 1,
            '<bos>': 2,  # Beginning of sequence
            '<eos>': 3,  # End of sequence
            '<human>': 4,  # Human message marker
            '<holly>': 5,  # Holly response marker
            '<think>': 6,  # Beginning of reasoning
            '</think>': 7,  # End of reasoning
            '<uncertain>': 8,  # Uncertainty marker
            '<question>': 9,  # Question marker
        }
        
        # Initialize vocabulary
        self.token_to_id = self.special_tokens.copy()
        self.id_to_token = {v: k for k, v in self.token_to_id.items()}
        
        # BPE merge rules
        self.merges = {}
        self.merge_order = []
        
        # Statistics for learning and adaptation
        self.token_frequencies = Counter()
        self.new_token_candidates = defaultdict(int)
        self.conversation_count = 0
        
        # Initialize with basic character vocabulary
        self._initialize_character_vocab()
    
    def _initialize_character_vocab(self):
        """Initialize with basic character-level vocabulary."""
        # Add common characters, digits, and punctuation
        chars = (
            'abcdefghijklmnopqrstuvwxyz'
            'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
            '0123456789'
            ' .,!?;:-()[]{}"\'\n\t'
        )
        
        current_id = len(self.special_tokens)
        for char in chars:
            if char not in self.token_to_id:
                self.token_to_id[char] = current_id
                self.id_to_token[current_id] = char
                current_id += 1
    
    def encode(self, text: str, add_special_tokens: bool = True) -> List[int]:
        """
        Encode text to token IDs.
        
        Args:
            text: Input text to encode
            add_special_tokens: Whether to add BOS/EOS tokens
            
        Returns:
            List of token IDs
        """
        # Preprocess text
        text = self._preprocess_text(text)
        
        # Start with character-level tokenization
        tokens = list(text)
        
        # Apply BPE merges
        tokens = self._apply_bpe_merges(tokens)
        
        # Convert to IDs
        token_ids = []
        if add_special_tokens:
            token_ids.append(self.token_to_id['<bos>'])
        
        for token in tokens:
            if token in self.token_to_id:
                token_ids.append(self.token_to_id[token])
                self.token_frequencies[token] += 1
            else:
                # Handle unknown tokens
                token_ids.append(self.token_to_id['<unk>'])
                self.new_token_candidates[token] += 1
        
        if add_special_tokens:
            token_ids.append(self.token_to_id['<eos>'])
        
        return token_ids
    
    def decode(self, token_ids: List[int], skip_special_tokens: bool = True) -> str:
        """
        Decode token IDs back to text.
        
        Args:
            token_ids: List of token IDs to decode
            skip_special_tokens: Whether to skip special tokens in output
            
        Returns:
            Decoded text string
        """
        tokens = []
        for token_id in token_ids:
            if token_id in self.id_to_token:
                token = self.id_to_token[token_id]
                if skip_special_tokens and token in self.special_tokens:
                    continue
                tokens.append(token)
            else:
                tokens.append('<unk>')
        
        # Join tokens and clean up
        text = ''.join(tokens)
        return self._postprocess_text(text)
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text before tokenization."""
        # Basic cleaning
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        text = text.strip()
        return text
    
    def _postprocess_text(self, text: str) -> str:
        """Postprocess text after decoding."""
        # Clean up spacing around punctuation
        text = re.sub(r'\s+([.,!?;:])', r'\1', text)
        text = re.sub(r'\(\s+', '(', text)
        text = re.sub(r'\s+\)', ')', text)
        return text
    
    def _apply_bpe_merges(self, tokens: List[str]) -> List[str]:
        """Apply learned BPE merges to token list."""
        if not self.merges:
            return tokens
        
        # Apply merges in order they were learned
        for merge_pair in self.merge_order:
            tokens = self._apply_single_merge(tokens, merge_pair)
        
        return tokens
    
    def _apply_single_merge(self, tokens: List[str], merge_pair: Tuple[str, str]) -> List[str]:
        """Apply a single BPE merge to token list."""
        left, right = merge_pair
        merged_token = left + right
        
        new_tokens = []
        i = 0
        while i < len(tokens):
            if i < len(tokens) - 1 and tokens[i] == left and tokens[i + 1] == right:
                new_tokens.append(merged_token)
                i += 2
            else:
                new_tokens.append(tokens[i])
                i += 1
        
        return new_tokens
    
    def learn_from_text(self, text: str, update_vocab: bool = True):
        """
        Learn new tokens and merges from text.
        
        This allows Holly to expand her vocabulary based on conversations.
        """
        self.conversation_count += 1
        
        # Encode text to identify new token candidates
        self.encode(text, add_special_tokens=False)
        
        if update_vocab and self.conversation_count % 10 == 0:
            # Periodically update vocabulary with frequent new candidates
            self._update_vocabulary()
    
    def _update_vocabulary(self):
        """Update vocabulary with frequent new token candidates."""
        # Find candidates that appear frequently enough
        frequent_candidates = [
            token for token, freq in self.new_token_candidates.items()
            if freq >= self.min_frequency and len(self.token_to_id) < self.vocab_size
        ]
        
        # Add new tokens to vocabulary
        current_id = max(self.id_to_token.keys()) + 1
        for token in frequent_candidates[:min(100, self.vocab_size - len(self.token_to_id))]:
            self.token_to_id[token] = current_id
            self.id_to_token[current_id] = token
            current_id += 1
            
            # Remove from candidates
            del self.new_token_candidates[token]
    
    def get_vocab_size(self) -> int:
        """Get current vocabulary size."""
        return len(self.token_to_id)
    
    def get_tokenizer_stats(self) -> Dict:
        """Get statistics about tokenizer usage and learning."""
        return {
            "vocab_size": len(self.token_to_id),
            "max_vocab_size": self.vocab_size,
            "conversation_count": self.conversation_count,
            "new_candidates": len(self.new_token_candidates),
            "most_frequent_tokens": self.token_frequencies.most_common(10),
            "top_new_candidates": list(self.new_token_candidates.most_common(10)),
            "special_tokens": list(self.special_tokens.keys())
        }
    
    def save_vocabulary(self, filepath: str):
        """Save vocabulary and merges to file."""
        vocab_data = {
            "token_to_id": self.token_to_id,
            "merges": self.merges,
            "merge_order": self.merge_order,
            "special_tokens": self.special_tokens,
            "vocab_size": self.vocab_size,
            "min_frequency": self.min_frequency
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, indent=2, ensure_ascii=False)
    
    def load_vocabulary(self, filepath: str):
        """Load vocabulary and merges from file."""
        with open(filepath, 'r', encoding='utf-8') as f:
            vocab_data = json.load(f)
        
        self.token_to_id = vocab_data["token_to_id"]
        self.id_to_token = {int(k): v for k, v in vocab_data.get("id_to_token", {}).items()}
        
        # Rebuild id_to_token if not present
        if not self.id_to_token:
            self.id_to_token = {v: k for k, v in self.token_to_id.items()}
        
        self.merges = vocab_data.get("merges", {})
        self.merge_order = vocab_data.get("merge_order", [])
        self.special_tokens = vocab_data.get("special_tokens", self.special_tokens)
        self.vocab_size = vocab_data.get("vocab_size", self.vocab_size)
        self.min_frequency = vocab_data.get("min_frequency", self.min_frequency)
