#!/usr/bin/env python3
"""
Restart <PERSON> with improved settings and training.

This script stops the current <PERSON> instance and restarts her
with better language capabilities.
"""

import subprocess
import sys
import time
import os
import requests
from pathlib import Path

def stop_current_holly():
    """Stop the current Holly server if running."""
    print("🛑 Stopping current Holly server...")
    
    # Try to gracefully stop via API (if available)
    try:
        response = requests.get("http://localhost:8000/api/insights", timeout=2)
        if response.status_code == 200:
            print("   Holly server is running, attempting graceful shutdown...")
            # Note: We don't have a shutdown endpoint, so we'll need to kill the process
    except:
        print("   No Holly server detected or already stopped")
    
    # Kill any Python processes running Holly (Windows-specific)
    try:
        subprocess.run(["taskkill", "/F", "/IM", "python.exe", "/FI", "WINDOWTITLE eq Holly*"], 
                      capture_output=True, check=False)
        print("   ✅ Stopped any running Holly processes")
    except:
        print("   ⚠️  Could not stop processes automatically")
    
    time.sleep(2)

def train_holly_if_needed():
    """Train Holly on basic language if not already done."""
    trained_model_path = "models/holly_trained.pt"
    
    if os.path.exists(trained_model_path):
        print(f"✅ Found trained model: {trained_model_path}")
        return trained_model_path
    
    print("🎓 Training Holly on basic language patterns...")
    print("   This will take a few minutes...")
    
    try:
        # Run the training script
        result = subprocess.run([sys.executable, "train_basic_language.py"], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Training completed successfully!")
            if os.path.exists(trained_model_path):
                return trained_model_path
            else:
                print("⚠️  Training completed but model file not found")
                return "models/holly_initial.pt"
        else:
            print(f"❌ Training failed: {result.stderr}")
            return "models/holly_initial.pt"
            
    except subprocess.TimeoutExpired:
        print("⏰ Training timed out, using original model")
        return "models/holly_initial.pt"
    except Exception as e:
        print(f"❌ Training error: {e}")
        return "models/holly_initial.pt"

def start_improved_holly(model_path):
    """Start Holly with the improved model."""
    print(f"🚀 Starting improved Holly with model: {model_path}")
    
    # Start Holly server with the specified model
    cmd = [
        sys.executable, 
        "holly/server.py",
        "--model-path", model_path,
        "--host", "localhost",
        "--port", "8000"
    ]
    
    print(f"   Command: {' '.join(cmd)}")
    
    try:
        # Start the server in a new process
        process = subprocess.Popen(cmd, cwd=os.getcwd())
        
        # Wait a moment for startup
        time.sleep(5)
        
        # Check if server is responding
        for attempt in range(10):
            try:
                response = requests.get("http://localhost:8000", timeout=2)
                if response.status_code == 200:
                    print("✅ Holly server is running successfully!")
                    print("🌐 Open http://localhost:8000 in your browser")
                    return process
            except:
                time.sleep(1)
        
        print("⚠️  Server started but may not be fully ready yet")
        print("🌐 Try opening http://localhost:8000 in your browser")
        return process
        
    except Exception as e:
        print(f"❌ Failed to start Holly: {e}")
        return None

def main():
    """Main function to restart Holly with improvements."""
    print("🌟 Holly Improvement & Restart Script")
    print("=" * 40)
    
    # Step 1: Stop current Holly
    stop_current_holly()
    
    # Step 2: Train Holly if needed
    model_path = train_holly_if_needed()
    
    # Step 3: Start improved Holly
    process = start_improved_holly(model_path)
    
    if process:
        print("\n🎉 Holly has been restarted with improvements!")
        print("\n📋 What's improved:")
        print("   • Better language patterns")
        print("   • More coherent responses")
        print("   • Proper English communication")
        print("   • Understanding of basic conversations")
        
        print("\n💡 Tips for better conversations:")
        print("   • Start with simple greetings")
        print("   • Correct her mistakes to help her learn")
        print("   • Use the 'Start Learning Session' button")
        print("   • Be patient as she continues to improve")
        
        print("\n🌐 Holly is ready at: http://localhost:8000")
        
        try:
            # Keep the script running to monitor the server
            print("\n⌨️  Press Ctrl+C to stop Holly")
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping Holly...")
            process.terminate()
            process.wait()
            print("👋 Holly stopped. Goodbye!")
    else:
        print("\n❌ Failed to start improved Holly")
        print("💡 Try running manually: python holly/server.py")

if __name__ == "__main__":
    main()
