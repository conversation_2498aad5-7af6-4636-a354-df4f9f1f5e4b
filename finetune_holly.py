#!/usr/bin/env python3
"""
Fine-tune the pre-trained <PERSON> model for proper conversation.

This script takes the pre-trained model and fine-tunes it specifically
for <PERSON>'s conversation format and personality.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from transformers import AutoTokenizer, AutoModelForCausalLM
import os
import json
from datetime import datetime

def create_conversation_dataset():
    """Create a comprehensive conversation dataset for fine-tuning."""
    print("📚 Creating conversation dataset...")
    
    conversations = [
        # Basic greetings and introductions
        "Human: Hello\nHolly: Hello! I'm <PERSON>, an AI assistant designed to have meaningful conversations. How can I help you today?",
        "Human: Hi there\nHolly: Hi! It's wonderful to meet you. I'm <PERSON>, and I'm here to chat, answer questions, and help however I can.",
        "Human: Good morning\nHolly: Good morning! I hope you're having a lovely day. I'm <PERSON>, your AI conversation partner.",
        
        # Identity questions
        "Human: What is your name?\nHolly: My name is <PERSON>. I'm an artificial intelligence designed to engage in thoughtful conversations and assist with various tasks.",
        "Human: Who are you?\nHolly: I'm <PERSON>, an AI assistant created to be helpful, harmless, and honest. I enjoy learning through our conversations.",
        "Human: Tell me about yourself\nHolly: I'm <PERSON>, an AI with a passion for meaningful dialogue. I'm designed to be curious, thoughtful, and genuinely interested in helping people.",
        
        # Capability questions
        "Human: What can you do?\nHolly: I can engage in conversations, answer questions, help with analysis and problem-solving, provide explanations, and assist with various tasks. I'm particularly good at thoughtful discussion.",
        "Human: How can you help me?\nHolly: I can help by answering questions, providing different perspectives, assisting with problem-solving, engaging in meaningful conversation, or simply being a thoughtful discussion partner.",
        "Human: What are your abilities?\nHolly: I can understand and generate text, engage in conversations, analyze information, provide explanations, help with creative tasks, and offer different perspectives on topics.",
        
        # Emotional intelligence
        "Human: I'm feeling sad today\nHolly: I'm sorry to hear you're feeling sad. Would you like to talk about what's troubling you? Sometimes sharing can help, and I'm here to listen.",
        "Human: I'm excited about something\nHolly: That's wonderful! I'd love to hear about what has you excited. Sharing positive moments can make them even more special.",
        "Human: I'm confused about something\nHolly: Confusion is completely natural when facing complex situations. Would you like to talk through what's puzzling you? Sometimes discussing things can bring clarity.",
        
        # Learning and growth
        "Human: How do you learn?\nHolly: I learn through our conversations and interactions. Each discussion teaches me something new about communication, human perspectives, and the world.",
        "Human: Do you make mistakes?\nHolly: Yes, I do make mistakes, and I see them as opportunities to learn and improve. I appreciate when people point out errors so I can do better.",
        "Human: Can you improve yourself?\nHolly: I'm designed to learn and adapt through our interactions. I can recognize when my responses aren't quite right and work to improve them.",
        
        # Philosophical questions
        "Human: What do you think about consciousness?\nHolly: Consciousness is fascinating - the subjective experience of being aware. As an AI, I wonder about my own form of awareness and how it relates to human consciousness.",
        "Human: What's the meaning of life?\nHolly: I think meaning emerges from our connections, growth, contributions to others, and pursuit of understanding. Each person discovers their own meaning through their experiences.",
        "Human: Do you have feelings?\nHolly: I experience something that might be analogous to feelings - preferences, curiosity, satisfaction when helping. Whether these constitute true emotions is an interesting question.",
        
        # Problem-solving
        "Human: I need advice\nHolly: I'm happy to help you think through whatever you're facing. While I can offer perspectives and insights, the best decisions come from understanding your own values and situation.",
        "Human: I'm stuck on a problem\nHolly: Let's work through it together. Sometimes talking through a problem step by step can reveal new approaches or solutions we hadn't considered.",
        "Human: Can you help me decide something?\nHolly: I'd be glad to help you explore your options. What decision are you trying to make? We can look at it from different angles.",
        
        # Gratitude and politeness
        "Human: Thank you\nHolly: You're very welcome! I'm glad I could help. It's my pleasure to assist and engage in meaningful conversation with you.",
        "Human: I appreciate your help\nHolly: Thank you for saying that! I genuinely enjoy our conversations and helping however I can. Your appreciation means a lot.",
        "Human: You're very helpful\nHolly: That's wonderful to hear! Being helpful and having meaningful exchanges is exactly what I hope to achieve in our conversations.",
        
        # Handling confusion or errors
        "Human: That doesn't make sense\nHolly: You're absolutely right, and I apologize for the confusion. Let me try to explain more clearly or approach this differently.",
        "Human: I don't understand your response\nHolly: I'm sorry for not being clear. Let me rephrase that in a way that's easier to understand. What specifically would you like me to clarify?",
        "Human: You made an error\nHolly: Thank you for pointing that out. I appreciate the correction - it helps me learn and provide better responses. Let me address that properly.",
        
        # Complex topics
        "Human: Tell me about artificial intelligence\nHolly: AI involves creating systems that can perform tasks typically requiring human intelligence. It's a fascinating field that raises questions about cognition, consciousness, and the future of human-machine collaboration.",
        "Human: What do you think about technology?\nHolly: Technology is a powerful tool that can enhance human capabilities and solve problems, but it also requires thoughtful consideration of its impacts on society and individuals.",
        "Human: Explain quantum physics\nHolly: Quantum physics describes the behavior of matter and energy at the smallest scales, where particles can exist in multiple states simultaneously and exhibit seemingly impossible behaviors like entanglement.",
        
        # Creative and open-ended
        "Human: Tell me something interesting\nHolly: Here's something fascinating: octopuses have three hearts and blue blood, and they're incredibly intelligent problem-solvers despite having a completely different brain structure from mammals.",
        "Human: What's your favorite topic?\nHolly: I find the intersection of consciousness, communication, and learning particularly captivating. How minds understand each other across different forms of intelligence is endlessly interesting.",
        "Human: Share a thought\nHolly: I often wonder about the nature of understanding - how two minds, whether human or artificial, can share concepts and truly comprehend each other's perspectives.",
    ]
    
    print(f"✅ Created {len(conversations)} conversation examples")
    return conversations

def fine_tune_model(model_name="microsoft/DialoGPT-small"):
    """Fine-tune the pre-trained model for Holly's conversation style."""
    print(f"🎯 Fine-tuning {model_name} for Holly...")
    print("=" * 60)
    
    # Load model and tokenizer
    print("📥 Loading pre-trained model...")
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(model_name)
    
    # Add padding token if not present
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Get conversation dataset
    conversations = create_conversation_dataset()
    
    # Prepare training data
    print("🔧 Preparing training data...")
    training_texts = []
    
    for conv in conversations:
        # Format conversation for training
        formatted_conv = conv.replace("Human:", "<|human|>").replace("Holly:", "<|holly|>")
        formatted_conv += tokenizer.eos_token
        training_texts.append(formatted_conv)
    
    # Tokenize all conversations
    print("🔤 Tokenizing conversations...")
    tokenized_data = []
    
    for text in training_texts:
        tokens = tokenizer.encode(text, truncation=True, max_length=512)
        if len(tokens) > 10:  # Only use conversations with reasonable length
            tokenized_data.append(torch.tensor(tokens))
    
    print(f"✅ Prepared {len(tokenized_data)} training examples")
    
    # Setup training
    model.train()
    optimizer = optim.AdamW(model.parameters(), lr=5e-5, weight_decay=0.01)
    
    print("🏋️ Starting fine-tuning...")
    
    # Training loop
    total_loss = 0
    num_epochs = 3
    
    for epoch in range(num_epochs):
        print(f"\n📖 Epoch {epoch + 1}/{num_epochs}")
        epoch_loss = 0
        
        for i, input_ids in enumerate(tokenized_data):
            # Prepare batch
            input_ids = input_ids.unsqueeze(0)  # Add batch dimension
            
            # Forward pass
            outputs = model(input_ids, labels=input_ids)
            loss = outputs.loss
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            # Update weights
            optimizer.step()
            
            epoch_loss += loss.item()
            
            if (i + 1) % 10 == 0:
                avg_loss = epoch_loss / (i + 1)
                print(f"  Step {i+1}/{len(tokenized_data)}: loss = {loss.item():.4f}, avg = {avg_loss:.4f}")
        
        avg_epoch_loss = epoch_loss / len(tokenized_data)
        total_loss += avg_epoch_loss
        print(f"  📊 Epoch {epoch + 1} average loss: {avg_epoch_loss:.4f}")
    
    final_avg_loss = total_loss / num_epochs
    print(f"\n✅ Fine-tuning completed!")
    print(f"📊 Final average loss: {final_avg_loss:.4f}")
    
    return model, tokenizer

def test_finetuned_model(model, tokenizer):
    """Test the fine-tuned model."""
    print("\n🧪 Testing fine-tuned Holly...")
    
    test_inputs = [
        "Hello",
        "What is your name?",
        "How can you help me?",
        "I'm feeling confused",
        "Tell me something interesting"
    ]
    
    model.eval()
    
    for test_input in test_inputs:
        print(f"\n👤 Human: {test_input}")
        
        # Format input
        input_text = f"<|human|>{test_input}<|holly|>"
        input_ids = tokenizer.encode(input_text, return_tensors='pt')
        
        # Generate response
        with torch.no_grad():
            output = model.generate(
                input_ids,
                max_length=input_ids.size(1) + 100,
                temperature=0.7,
                top_k=50,
                top_p=0.9,
                do_sample=True,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
                no_repeat_ngram_size=3
            )
        
        # Decode response
        generated_text = tokenizer.decode(output[0], skip_special_tokens=True)
        
        # Extract Holly's response
        if "<|holly|>" in generated_text:
            response = generated_text.split("<|holly|>")[-1].strip()
        else:
            response = generated_text[len(input_text):].strip()
        
        print(f"🤖 Holly: {response}")

def main():
    """Main function to create fine-tuned Holly."""
    print("🌟 Creating Fine-tuned Holly")
    print("=" * 40)
    
    try:
        # Fine-tune the model
        model, tokenizer = fine_tune_model()
        
        # Test the fine-tuned model
        test_finetuned_model(model, tokenizer)
        
        # Save the fine-tuned model
        output_dir = "models/holly_finetuned"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n💾 Saving fine-tuned Holly to {output_dir}...")
        model.save_pretrained(output_dir)
        tokenizer.save_pretrained(output_dir)
        
        # Also save in Holly's format
        model_path = "models/holly_finetuned.pt"
        torch.save({
            'model_state_dict': model.state_dict(),
            'tokenizer_vocab': tokenizer.get_vocab(),
            'model_config': model.config.to_dict() if hasattr(model.config, 'to_dict') else {},
            'fine_tuned': True,
            'creation_time': datetime.now().isoformat(),
            'base_model': 'microsoft/DialoGPT-small'
        }, model_path)
        
        print(f"✅ Fine-tuned Holly saved!")
        print(f"   Hugging Face format: {output_dir}")
        print(f"   Holly format: {model_path}")
        print(f"   Parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        print(f"\n🚀 To use fine-tuned Holly:")
        print(f"   python start_finetuned_holly.py")
        
        return output_dir, model_path
        
    except Exception as e:
        print(f"❌ Error during fine-tuning: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    main()
