"""
Chat interface for <PERSON>.

This module provides the core chat functionality and
conversation management for <PERSON>'s web interface.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import asyncio
import torch
from holly.core.self_improvement import SelfImprovementTools


class ChatInterface:
    """
    Core chat interface for <PERSON>.
    
    Manages conversation flow, message handling,
    and integration with <PERSON>'s learning systems.
    """
    
    def __init__(self, model, tokenizer, trainer=None):
        self.model = model
        self.tokenizer = tokenizer
        self.trainer = trainer

        # Self-improvement tools
        self.self_improvement = SelfImprovementTools(model, tokenizer)

        # Conversation state
        self.conversation_history = []
        self.current_session = None
        self.message_count = 0

        # Chat settings
        self.default_settings = {
            'temperature': 0.8,
            'max_length': 150,
            'show_reasoning': False,
            'learning_enabled': True,
            'self_improvement_enabled': True  # New setting
        }
    
    def start_conversation(self, session_id: Optional[str] = None) -> str:
        """Start a new conversation session."""
        if session_id is None:
            session_id = f"chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = session_id
        self.conversation_history = []
        self.message_count = 0
        
        # Add welcome message
        welcome_message = {
            'role': 'holly',
            'content': "Hello! I'm <PERSON>, an experimental AI designed to learn and grow through our conversations. How can we explore together today?",
            'timestamp': datetime.now().isoformat(),
            'message_id': self.message_count
        }
        
        self.conversation_history.append(welcome_message)
        self.message_count += 1
        
        return session_id
    
    def send_message(
        self,
        message: str,
        settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send a message to Holly and get a response.
        
        Args:
            message: User message
            settings: Optional chat settings override
            
        Returns:
            Response data including Holly's reply and metadata
        """
        # Use default settings if not provided
        chat_settings = {**self.default_settings, **(settings or {})}
        
        # Add user message to history
        user_message = {
            'role': 'human',
            'content': message,
            'timestamp': datetime.now().isoformat(),
            'message_id': self.message_count
        }
        
        self.conversation_history.append(user_message)
        self.message_count += 1
        
        # Generate Holly's response
        response_data = self._generate_response(message, chat_settings)
        
        # Add Holly's response to history
        holly_message = {
            'role': 'holly',
            'content': response_data['response'],
            'timestamp': datetime.now().isoformat(),
            'message_id': self.message_count,
            'metadata': response_data.get('metadata', {})
        }
        
        self.conversation_history.append(holly_message)
        self.message_count += 1
        
        # Learn from conversation if enabled
        if chat_settings.get('learning_enabled') and self.trainer:
            learning_result = self._learn_from_exchange(user_message, holly_message)
            response_data['learning_result'] = learning_result
        
        return response_data
    
    def _generate_response(
        self,
        message: str,
        settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate Holly's response to a message."""
        # Prepare conversation context
        context = self._format_conversation_context()
        full_input = f"{context}<human>{message}<eos>"

        # Tokenize input
        input_ids = self.tokenizer.encode(full_input)
        input_tensor = torch.tensor([input_ids])

        # Generate initial response
        with torch.no_grad():
            generation_result = self.model.generate(
                input_tensor,
                max_length=settings['max_length'],
                temperature=settings['temperature'],
                show_reasoning=settings['show_reasoning']
            )

        # Decode response
        new_tokens = generation_result['new_tokens'][0].tolist()
        response_text = self.tokenizer.decode(new_tokens, skip_special_tokens=True)

        # Self-improvement check
        improvement_data = None
        if settings.get('self_improvement_enabled', True):
            improvement_result = self.self_improvement.apply_self_correction(
                response_text,
                message
            )

            if improvement_result['improvement_applied']:
                # Use the improved response
                response_text = improvement_result['improved_response']
                improvement_data = {
                    'original_response': improvement_result.get('original_analysis', {}).get('response', ''),
                    'quality_improvement': improvement_result.get('quality_improvement', 0),
                    'issues_detected': improvement_result.get('original_analysis', {}).get('detected_issues', []),
                    'improvements_made': improvement_result.get('improvement_record', {}).get('reasoning', [])
                }

        # Prepare response data
        response_data = {
            'response': response_text,
            'generation_stats': {
                'tokens_generated': generation_result['generation_length'],
                'temperature': settings['temperature'],
                'max_length': settings['max_length']
            },
            'metadata': {
                'session_id': self.current_session,
                'message_count': self.message_count,
                'timestamp': datetime.now().isoformat()
            }
        }

        # Add self-improvement data if available
        if improvement_data:
            response_data['self_improvement'] = improvement_data

        # Add reasoning if requested
        if settings['show_reasoning'] and 'reasoning_trace' in generation_result:
            response_data['reasoning'] = generation_result['reasoning_trace']

        return response_data
    
    def _format_conversation_context(self, max_context: int = 5) -> str:
        """Format recent conversation history for model input."""
        # Get recent messages (excluding the current exchange)
        recent_messages = self.conversation_history[-max_context*2:]
        
        context = ""
        for msg in recent_messages:
            if msg['role'] == 'human':
                context += f"<human>{msg['content']}<eos>"
            elif msg['role'] == 'holly':
                context += f"<holly>{msg['content']}<eos>"
        
        return context
    
    def _learn_from_exchange(
        self, 
        user_message: Dict[str, Any], 
        holly_message: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Learn from the conversation exchange."""
        if not self.trainer:
            return None
        
        conversation_data = [
            {
                'role': user_message['role'],
                'content': user_message['content']
            },
            {
                'role': holly_message['role'],
                'content': holly_message['content']
            }
        ]
        
        try:
            learning_result = self.trainer.learn_from_conversation(
                conversation_data,
                session_id=self.current_session,
                apply_immediately=True
            )
            return learning_result
        except Exception as e:
            return {'error': f'Learning failed: {str(e)}'}
    
    def provide_feedback(
        self,
        message_id: int,
        feedback_type: str,
        feedback_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Provide feedback on a specific message.
        
        Args:
            message_id: ID of the message to provide feedback on
            feedback_type: Type of feedback (correction, rating, etc.)
            feedback_data: Feedback details
            
        Returns:
            Feedback processing result
        """
        # Find the message
        target_message = None
        for msg in self.conversation_history:
            if msg.get('message_id') == message_id:
                target_message = msg
                break
        
        if not target_message:
            return {'error': 'Message not found'}
        
        if target_message['role'] != 'holly':
            return {'error': 'Can only provide feedback on Holly\'s messages'}
        
        # Process feedback based on type
        if feedback_type == 'correction' and self.trainer:
            original_response = target_message['content']
            corrected_response = feedback_data.get('corrected_response', '')
            
            feedback_result = self.trainer.process_feedback(
                original_response,
                corrected_response,
                feedback_type='correction',
                session_id=self.current_session
            )
            
            return feedback_result
        
        elif feedback_type == 'rating':
            rating = feedback_data.get('rating', 0.5)
            criteria = feedback_data.get('criteria', [])
            
            # Store rating feedback (could be processed by trainer)
            feedback_record = {
                'message_id': message_id,
                'type': 'rating',
                'rating': rating,
                'criteria': criteria,
                'timestamp': datetime.now().isoformat()
            }
            
            return feedback_record
        
        else:
            return {'error': f'Unsupported feedback type: {feedback_type}'}
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary of current conversation."""
        human_messages = [msg for msg in self.conversation_history if msg['role'] == 'human']
        holly_messages = [msg for msg in self.conversation_history if msg['role'] == 'holly']
        
        return {
            'session_id': self.current_session,
            'total_messages': len(self.conversation_history),
            'human_messages': len(human_messages),
            'holly_messages': len(holly_messages),
            'conversation_start': self.conversation_history[0]['timestamp'] if self.conversation_history else None,
            'last_message': self.conversation_history[-1]['timestamp'] if self.conversation_history else None,
            'message_count': self.message_count
        }
    
    def export_conversation(self, format: str = 'json') -> str:
        """Export conversation in specified format."""
        if format == 'json':
            return json.dumps({
                'session_id': self.current_session,
                'conversation': self.conversation_history,
                'summary': self.get_conversation_summary(),
                'export_timestamp': datetime.now().isoformat()
            }, indent=2)
        
        elif format == 'text':
            text_export = f"Holly Conversation - Session: {self.current_session}\n"
            text_export += "=" * 50 + "\n\n"
            
            for msg in self.conversation_history:
                role = "You" if msg['role'] == 'human' else "Holly"
                timestamp = msg['timestamp']
                content = msg['content']
                text_export += f"[{timestamp}] {role}: {content}\n\n"
            
            return text_export
        
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def clear_conversation(self):
        """Clear current conversation history."""
        self.conversation_history = []
        self.message_count = 0
        self.current_session = None
    
    def get_chat_settings(self) -> Dict[str, Any]:
        """Get current chat settings."""
        return self.default_settings.copy()
    
    def update_chat_settings(self, new_settings: Dict[str, Any]):
        """Update chat settings."""
        self.default_settings.update(new_settings)

    def get_self_improvement_insights(self) -> Dict[str, Any]:
        """Get insights about Holly's self-improvement efforts."""
        return {
            'improvement_history': self.self_improvement.get_improvement_history(),
            'current_settings': self.default_settings,
            'conversation_summary': self.get_conversation_summary()
        }

    def analyze_last_response(self) -> Dict[str, Any]:
        """Analyze the quality of Holly's last response."""
        if not self.conversation_history:
            return {'error': 'No conversation history available'}

        # Find the last Holly response
        last_holly_message = None
        last_human_message = None

        for i in range(len(self.conversation_history) - 1, -1, -1):
            msg = self.conversation_history[i]
            if msg['role'] == 'holly' and last_holly_message is None:
                last_holly_message = msg
            elif msg['role'] == 'human' and last_human_message is None and last_holly_message is not None:
                last_human_message = msg
                break

        if not last_holly_message:
            return {'error': 'No Holly responses found'}

        context = last_human_message['content'] if last_human_message else ""
        analysis = self.self_improvement.analyze_response_quality(
            last_holly_message['content'],
            context
        )

        return {
            'analysis': analysis,
            'human_message': last_human_message['content'] if last_human_message else None,
            'holly_response': last_holly_message['content'],
            'timestamp': last_holly_message['timestamp']
        }
